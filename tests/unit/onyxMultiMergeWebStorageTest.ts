import OnyxCache from '../../lib/OnyxCache';
import waitForPromisesToResolve from '../utils/waitForPromisesToResolve';
import Storage from '../../lib/storage';
import type MockedStorage from '../../lib/storage/__mocks__';
import type OnyxInstance from '../../lib/Onyx';
import type GenericCollection from '../utils/GenericCollection';

const StorageMock = Storage as unknown as typeof MockedStorage;

const ONYX_KEYS = {
    COLLECTION: {
        TEST_KEY: 'test_',
    },
};

const initialTestObject = {a: 'a'};
const initialData = {
    test_1: initialTestObject,
    test_2: initialTestObject,
    test_3: initialTestObject,
};

describe('Onyx.mergeCollection() and WebStorage', () => {
    let Onyx: typeof OnyxInstance;

    beforeAll(() => {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        Onyx = require('../../lib').default;
        jest.useRealTimers();

        Onyx.init({
            keys: ONYX_KEYS,
            initialKeyStates: {},
        });
    });

    afterEach(() => Onyx.clear());

    it('merges two sets of data consecutively', () => {
        StorageMock.setMockStore(initialData);

        // Given initial data in storage
        expect(StorageMock.getMockStore().test_1).toEqual(initialTestObject);
        expect(StorageMock.getMockStore().test_2).toEqual(initialTestObject);
        expect(StorageMock.getMockStore().test_3).toEqual(initialTestObject);

        // And an empty cache values for the collection keys
        expect(OnyxCache.get('test_1')).not.toBeDefined();
        expect(OnyxCache.get('test_2')).not.toBeDefined();
        expect(OnyxCache.get('test_3')).not.toBeDefined();

        // When we merge additional data
        const additionalDataOne = {b: 'b', c: 'c', e: [1, 2]};
        Onyx.mergeCollection(ONYX_KEYS.COLLECTION.TEST_KEY, {
            test_1: additionalDataOne,
            test_2: additionalDataOne,
            test_3: additionalDataOne,
        } as GenericCollection);

        // And call again consecutively with different data
        const additionalDataTwo = {d: 'd', e: [2]};
        Onyx.mergeCollection(ONYX_KEYS.COLLECTION.TEST_KEY, {
            test_1: additionalDataTwo,
            test_2: additionalDataTwo,
            test_3: additionalDataTwo,
        } as GenericCollection);

        return waitForPromisesToResolve().then(() => {
            const finalObject = {
                a: 'a',
                b: 'b',
                c: 'c',
                d: 'd',
                e: [2],
            };

            // Then our new data should merge with the existing data in the cache
            expect(OnyxCache.get('test_1')).toEqual(finalObject);
            expect(OnyxCache.get('test_2')).toEqual(finalObject);
            expect(OnyxCache.get('test_3')).toEqual(finalObject);

            // And the storage should reflect the same state
            expect(StorageMock.getMockStore().test_1).toEqual(finalObject);
            expect(StorageMock.getMockStore().test_2).toEqual(finalObject);
            expect(StorageMock.getMockStore().test_3).toEqual(finalObject);
        });
    });

    it('cache updates correctly when accessed again if keys are removed or evicted', () => {
        // Given empty storage
        expect(StorageMock.getMockStore().test_1).toBeFalsy();
        expect(StorageMock.getMockStore().test_2).toBeFalsy();
        expect(StorageMock.getMockStore().test_3).toBeFalsy();

        // And an empty cache values for the collection keys
        expect(OnyxCache.get('test_1')).toBeFalsy();
        expect(OnyxCache.get('test_2')).toBeFalsy();
        expect(OnyxCache.get('test_3')).toBeFalsy();

        // When we merge additional data and wait for the change
        const data = {a: 'a', b: 'b'};
        Onyx.mergeCollection(ONYX_KEYS.COLLECTION.TEST_KEY, {
            test_1: data,
            test_2: data,
            test_3: data,
        } as GenericCollection);

        return waitForPromisesToResolve()
            .then(() => {
                // Then the cache and storage should match
                expect(OnyxCache.get('test_1')).toEqual(data);
                expect(OnyxCache.get('test_2')).toEqual(data);
                expect(OnyxCache.get('test_3')).toEqual(data);
                expect(StorageMock.getMockStore().test_1).toEqual(data);
                expect(StorageMock.getMockStore().test_2).toEqual(data);
                expect(StorageMock.getMockStore().test_3).toEqual(data);

                // When we drop all the cache keys (but do not modify the underlying storage) and merge another object
                OnyxCache.drop('test_1');
                OnyxCache.drop('test_2');
                OnyxCache.drop('test_3');

                const additionalData = {c: 'c'};
                Onyx.mergeCollection(ONYX_KEYS.COLLECTION.TEST_KEY, {
                    test_1: additionalData,
                    test_2: additionalData,
                    test_3: additionalData,
                } as GenericCollection);

                return waitForPromisesToResolve();
            })
            .then(() => {
                const finalObject = {
                    a: 'a',
                    b: 'b',
                    c: 'c',
                };

                // Then our new data should merge with the existing data in the cache
                expect(OnyxCache.get('test_1')).toEqual(finalObject);
                expect(OnyxCache.get('test_2')).toEqual(finalObject);
                expect(OnyxCache.get('test_3')).toEqual(finalObject);

                // And the storage should reflect the same state
                expect(StorageMock.getMockStore().test_1).toEqual(finalObject);
                expect(StorageMock.getMockStore().test_2).toEqual(finalObject);
                expect(StorageMock.getMockStore().test_3).toEqual(finalObject);
            });
    });

    it('setItem() and multiMerge()', () => {
        // Onyx should be empty after clear() is called
        expect(StorageMock.getMockStore()).toEqual({});

        // Given no previous data and several calls to setItem and call to mergeCollection to update a given key

        // 1st call
        Onyx.set('test_1', {a: 'a'});

        // These merges will all queue together
        Onyx.merge('test_1', {b: 'b'});
        Onyx.merge('test_1', {c: 'c'});

        // 2nd call
        Onyx.mergeCollection(ONYX_KEYS.COLLECTION.TEST_KEY, {
            test_1: {d: 'd', e: 'e'},
        } as GenericCollection);

        // Last call
        Onyx.merge('test_1', {f: 'f'});
        return waitForPromisesToResolve().then(() => {
            const finalObject = {
                a: 'a',
                b: 'b',
                c: 'c',
                d: 'd',
                e: 'e',
                f: 'f',
            };

            expect(OnyxCache.get('test_1')).toEqual(finalObject);
            expect(StorageMock.getMockStore().test_1).toEqual(finalObject);
        });
    });
});
