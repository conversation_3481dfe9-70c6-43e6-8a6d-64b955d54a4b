.DS_Store
.cache
.vscode
.idea
.expo
.gradle
.classpath
.project
.settings
.history

local.properties

/coverage/
/dist/
/lib/

node_modules/

xcuserdata
generated
dist
lib
build

npm-debug.*

*.tsbuildinfo
*.log
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*

*.iml

# @generated expo-cli sync-e7dcf75f4e856f7b6f3239b3f3a7dd614ee755a8
# The following patterns were generated by expo-cli

# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace
.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/
dist/

# @end expo-cli

# Yarn
.yarn-global
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Local Netlify folder
.netlify
