{"name": "@expensify/react-native-live-markdown", "version": "0.1.244", "description": "Drop-in replacement for React Native's TextInput component with Markdown formatting.", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/src/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "mock", "android", "apple", "cpp", "*.podsp<PERSON>", "react-native.config.js", "!apple/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"example": "npm run --workspace=@expensify/react-native-live-markdown-example", "test": "jest", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.ts,.tsx", "lint:root": "eslint . --ext .js,.ts,.tsx --ignore-pattern '/example/**/*' --ignore-pattern '/WebExample/**/*'", "lint:example": "eslint example --ext .js,.ts,.tsx", "lint:WebExample": "eslint <PERSON> --ext .js,.ts,.tsx", "clean": "del-cli android/build example/android/build example/android/app/build example/ios/build lib", "prepare": "patch-package && bob build", "build:watch": "nodemon --watch src --ext .ts,.tsx,.css --exec \"rm -f .build_complete && npm run prepare && npm pack && touch .build_complete\"", "release": "release-it"}, "keywords": ["react-native", "ios", "android"], "repository": {"type": "git", "url": "git+https://github.com/Expensify/react-native-live-markdown.git"}, "author": "Expensify, Inc.", "license": "MIT", "bugs": {"url": "https://github.com/expensify/react-native-live-markdown/issues"}, "homepage": "https://github.com/expensify/react-native-live-markdown#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.18.6", "@commitlint/config-conventional": "^17.0.2", "@evilmartians/lefthook": "^1.5.0", "@lwc/eslint-plugin-lwc": "^1.7.2", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/eslint-config": "0.77.0", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^29.5.14", "@types/react": "~18.2.6", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "del-cli": "^5.0.0", "eslint": "^8.56.0", "eslint-config-expensify": "^2.0.24", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-tsdoc": "^0.2.17", "expensify-common": "2.0.115", "jest": "^29.6.3", "jest-environment-jsdom": "^29.7.0", "nodemon": "^3.1.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react": "18.3.1", "react-native": "0.77.0", "react-native-builder-bob": "^0.20.0", "react-native-reanimated": "3.17.0", "react-native-web": "^0.19.10", "release-it": "^15.0.0", "turbo": "^1.10.7", "typescript": "~5.3.3"}, "peerDependencies": {"expensify-common": ">=2.0.115", "react": "*", "react-native": "*", "react-native-reanimated": ">=3.17.0"}, "workspaces": ["./example", "./WebExample"], "engines": {"node": ">= 18.0.0"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"], "testEnvironment": "jsdom", "testPathIgnorePatterns": ["/WebExample/__tests__/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.json"}]]}, "codegenConfig": {"name": "RNLiveMarkdownSpec", "type": "all", "jsSrcsDir": "src"}}