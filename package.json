{"name": "react-native-onyx", "version": "2.0.105", "author": "Expensify, Inc.", "homepage": "https://expensify.com", "description": "State management for React Native", "license": "MIT", "private": false, "keywords": ["React Native", "React", "Persistent storage", "Pub/Sub"], "repository": {"type": "git", "url": "https://github.com/Expensify/react-native-onyx.git"}, "bugs": {"url": "https://github.com/Expensify/react-native-onyx/issues"}, "files": ["dist/**/*", "API.md", "README.md", "LICENSE.md"], "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepare": "npm run build", "lint": "eslint .", "typecheck": "tsc --noEmit", "test": "jest", "perf-test": "npx reassure", "build": "tsc -p tsconfig.build.json", "build:watch": "nodemon --watch lib --ext js,json,ts,tsx --exec \"npm run build && npm pack\"", "prebuild:docs": "npm run build", "build:docs": "ts-node buildDocs.ts", "lint-tests": "eslint tests/**", "prettier": "prettier --write .", "gh-actions-build": "./.github/scripts/buildActions.sh"}, "dependencies": {"ascii-table": "0.0.9", "fast-equals": "^4.0.3", "lodash.bindall": "^4.4.0", "lodash.clone": "^4.5.0", "lodash.pick": "^4.4.0", "lodash.transform": "^4.6.0", "underscore": "^1.13.6"}, "devDependencies": {"@actions/core": "^1.10.1", "@jest/globals": "^29.7.0", "@lwc/eslint-plugin-lwc": "^1.7.2", "@ngneat/falso": "^7.2.0", "@react-native-community/eslint-config": "^3.2.0", "@react-native/polyfills": "^2.0.0", "@testing-library/jest-native": "^3.4.2", "@testing-library/react-native": "^10.0.0", "@types/jest": "^28.1.8", "@types/jsdoc-to-markdown": "^7.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20.11.5", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.18", "@types/react-native": "^0.70.0", "@types/underscore": "^1.11.15", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vercel/ncc": "0.38.1", "date-fns": "^4.1.0", "eslint": "^8.56.0", "eslint-config-expensify": "^2.0.81", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.31.10", "idb-keyval": "^6.2.1", "jest": "^26.5.2", "jest-cli": "^26.5.2", "jsdoc-to-markdown": "^7.1.0", "metro-react-native-babel-preset": "^0.77.0", "nodemon": "^3.0.3", "prettier": "^2.8.8", "prop-types": "^15.7.2", "react": "18.2.0", "react-dom": "^18.2.0", "react-native": "0.76.3", "react-native-device-info": "^10.3.0", "react-native-nitro-modules": "^0.24.1", "react-native-nitro-sqlite": "^9.1.8", "react-native-performance": "^2.0.0", "react-test-renderer": "18.1.0", "reassure": "1.4.0", "ts-node": "^10.9.2", "type-fest": "^3.12.0", "typescript": "^5.4.5"}, "peerDependencies": {"idb-keyval": "^6.2.1", "react": ">=18.1.0", "react-native": ">=0.75.0", "react-dom": ">=18.1.0", "react-native-device-info": "^10.3.0", "react-native-nitro-modules": ">=0.24.1", "react-native-nitro-sqlite": "^9.1.4", "react-native-performance": "^5.1.0"}, "peerDependenciesMeta": {"idb-keyval": {"optional": true}, "react-native-performance": {"optional": true}, "react-native-nitro-modules": {"optional": true}, "react-native-nitro-sqlite": {"optional": true}, "react-native-device-info": {"optional": true}}, "engines": {"node": ">=20.18.1", "npm": ">=10.8.2"}, "sideEffects": false}