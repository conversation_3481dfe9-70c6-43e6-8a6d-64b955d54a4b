{"description": "Routing and navigation for your React Native apps", "private": true, "workspaces": {"packages": ["packages/*", "example"]}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/react-navigation/react-navigation.git"}, "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/satya164/), <PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/osdnk/)", "scripts": {"lint": "eslint \"**/*.{js,ts,tsx}\"", "typecheck": "tsc --noEmit --composite false", "test": "jest", "clean": "<PERSON>rna run clean", "build": "lerna run prepack", "publish": "lerna <PERSON>", "release": "run-s build publish", "example": "yarn workspace @react-navigation/example"}, "engines": {"yarn": "3.2.2", "node": ">=18"}, "packageManager": "yarn@4.0.1", "devDependencies": {"@commitlint/config-conventional": "^18.5.0", "@evilmartians/lefthook": "^1.6.0", "@lerna-lite/cli": "^3.9.3", "@lerna-lite/publish": "^3.9.3", "@lerna-lite/run": "^3.9.3", "@react-native/babel-preset": "^0.74.88", "babel-plugin-syntax-hermes-parser": "^0.25.1", "check-dependency-version-consistency": "^4.1.0", "commitlint": "^18.5.0", "eslint": "^8.56.0", "eslint-config-satya164": "^3.3.0", "eslint-plugin-simple-import-sort": "^10.0.0", "jest": "^29.7.0", "npm-run-all2": "^6.2.2", "prettier": "^3.2.4", "typescript": "^5.5.2"}, "jest": {"testEnvironment": "node", "testRegex": "/__tests__/.*\\.(test|spec)\\.(js|tsx?)$", "setupFilesAfterEnv": ["<rootDir>/jest/setup.js"], "transformIgnorePatterns": ["node_modules/(?!(@react-native|react-native|react-native-reanimated)/)"], "moduleNameMapper": {"@react-navigation/([^/]+)": "<rootDir>/packages/$1/src", "react-native-drawer-layout": "<rootDir>/packages/react-native-drawer-layout/src", "react-native-tab-view": "<rootDir>/packages/react-native-tab-view/src"}, "prettierPath": null, "preset": "react-native"}, "prettier": {"quoteProps": "consistent", "tabWidth": 2, "useTabs": false, "singleQuote": true, "trailingComma": "es5"}}