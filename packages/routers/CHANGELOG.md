# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [7.2.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.1.2...@react-navigation/routers@7.2.0) (2025-03-02)

### Bug Fixes

* pop for deep links to screens containing navigators ([05d2d97](https://github.com/react-navigation/react-navigation/commit/05d2d97157ac1abf17957ee402634aa651b053ba)) - by @satya164

### Features

* add a pop option to navigate ([492237d](https://github.com/react-navigation/react-navigation/commit/492237d4a5ac2c3a3095a9a429bb1a440260301a)) - by @satya164

## [7.1.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.1.1...@react-navigation/routers@7.1.2) (2024-12-12)

### Bug Fixes

* nanoid vulberable version ([#12328](https://github.com/react-navigation/react-navigation/issues/12328)) ([2a745c8](https://github.com/react-navigation/react-navigation/commit/2a745c8c598f95fcec5bbf5442045478d4046663)) - by @khushilms

## [7.1.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.1.0...@react-navigation/routers@7.1.1) (2024-11-28)

**Note:** Version bump only for package @react-navigation/routers

# [7.1.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0...@react-navigation/routers@7.1.0) (2024-11-26)

### Features

* add merge as third param to navigate and update tests ([b8bdd01](https://github.com/react-navigation/react-navigation/commit/b8bdd019b9e9381f7ae060eb3dd291b3bac3c4b7)) - by @

# [7.0.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.8...@react-navigation/routers@7.0.0) (2024-11-06)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-rc.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.7...@react-navigation/routers@7.0.0-rc.8) (2024-08-07)

### Bug Fixes

* improve how navigate and other methods are typed ([#12093](https://github.com/react-navigation/react-navigation/issues/12093)) ([a528b9b](https://github.com/react-navigation/react-navigation/commit/a528b9b407dbaeaac0caae8edcb5b3c6840144fa)) - by @satya164
* match push and jumpTo types to navigate ([23d9f2e](https://github.com/react-navigation/react-navigation/commit/23d9f2e2b03123cc2a5c984afc60c5276fee5c84)) - by @satya164

# [7.0.0-rc.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.6...@react-navigation/routers@7.0.0-rc.7) (2024-08-01)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-rc.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.5...@react-navigation/routers@7.0.0-rc.6) (2024-07-11)

### Bug Fixes

* upgrade react-native-builder-bob ([1575287](https://github.com/react-navigation/react-navigation/commit/1575287d40fadb97f33eb19c2914d8be3066b47a)) - by @

# [7.0.0-rc.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.4...@react-navigation/routers@7.0.0-rc.5) (2024-07-07)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-rc.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.3...@react-navigation/routers@7.0.0-rc.4) (2024-07-04)

### Bug Fixes

* fix published files ([829caa0](https://github.com/react-navigation/react-navigation/commit/829caa019e125811eea5213fd380e8e1bdbe7030)) - by @

# [7.0.0-rc.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.2...@react-navigation/routers@7.0.0-rc.3) (2024-07-04)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-rc.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.1...@react-navigation/routers@7.0.0-rc.2) (2024-07-04)

### Features

* add package.json exports field ([1435cfe](https://github.com/react-navigation/react-navigation/commit/1435cfe3300767c221ebd4613479ad662d61efee)) - by @

# [7.0.0-rc.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-rc.0...@react-navigation/routers@7.0.0-rc.1) (2024-07-01)

### Bug Fixes

* stop using react-native field in package.json ([efc33cb](https://github.com/react-navigation/react-navigation/commit/efc33cb0c4830a84ceae034dc1278c54f1faf32d)) - by @

# [7.0.0-rc.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.6...@react-navigation/routers@7.0.0-rc.0) (2024-06-27)

### Bug Fixes

* Invalid Property Access on non existent entry ([#11990](https://github.com/react-navigation/react-navigation/issues/11990)) ([dadd650](https://github.com/react-navigation/react-navigation/commit/dadd6505ca881032700ba37ee65c6ce69006d448)) - by @braandl

# [7.0.0-alpha.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.5...@react-navigation/routers@7.0.0-alpha.6) (2024-02-23)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-alpha.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.4...@react-navigation/routers@7.0.0-alpha.5) (2024-01-17)

### Bug Fixes

* follow-up for the retain-related changes in stack router ([#11768](https://github.com/react-navigation/react-navigation/issues/11768)) ([573d0b8](https://github.com/react-navigation/react-navigation/commit/573d0b8d6a1b4990894204f1dcb37cd204f1e74c)) - by @osdnk

### Features

* add retaining of the screen to the stack navigator ([#11765](https://github.com/react-navigation/react-navigation/issues/11765)) ([7fe82f7](https://github.com/react-navigation/react-navigation/commit/7fe82f7217d9d146e5a127bcac921f0c90e2059f)), closes [#11758](https://github.com/react-navigation/react-navigation/issues/11758) - by @osdnk
* preloading in routers  ([382d6e6](https://github.com/react-navigation/react-navigation/commit/382d6e6f3312630b34332b1ae7d4bd7bf9b4ee60)) - by @osdnk

# [7.0.0-alpha.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.3...@react-navigation/routers@7.0.0-alpha.4) (2023-11-17)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-alpha.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.2...@react-navigation/routers@7.0.0-alpha.3) (2023-11-12)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-alpha.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.1...@react-navigation/routers@7.0.0-alpha.2) (2023-09-07)

**Note:** Version bump only for package @react-navigation/routers

# [7.0.0-alpha.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@7.0.0-alpha.0...@react-navigation/routers@7.0.0-alpha.1) (2023-03-01)

### Bug Fixes

* fix paths in sourcemap files ([368e069](https://github.com/react-navigation/react-navigation/commit/368e0691b9fb07d4b1cbe71cfe4c2f40512f93ad)) - by @satya164
* handle getId in tab and drawer routers ([7430c72](https://github.com/react-navigation/react-navigation/commit/7430c723ced1101191cfbdf0c26d0b0947602b2b)) - by @satya164

# [7.0.0-alpha.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.1.3...@react-navigation/routers@7.0.0-alpha.0) (2023-02-17)

### Bug Fixes

* handle merge: true with NAVIGATION_DEPRECATED in TabRouter ([e5cf73d](https://github.com/react-navigation/react-navigation/commit/e5cf73d9bec5d763c284445547d4ae2650869201)) - by @satya164

* feat!: add `popTo` method for stack and remove going back behaviour of ([c9c2163](https://github.com/react-navigation/react-navigation/commit/c9c2163d28da963bd760cf395d17efe9b851f531)) - by @satya164
* refactor!: drop support for key property in navigate ([61c53bb](https://github.com/react-navigation/react-navigation/commit/61c53bb1836a47083b3b5ea0f4fddba6081885f2)) - by @satya164

### Features

* add navigateDeprecated for backward compatibility ([8ea6dc7](https://github.com/react-navigation/react-navigation/commit/8ea6dc793d8596da5e6052dbbae2e4825578dc50)) - by @satya164
* extract drawer to a separate package ([58b7cae](https://github.com/react-navigation/react-navigation/commit/58b7caeaad00eafbcda36561e75e538e0f02c4af)) - by @satya164

### BREAKING CHANGES

* Previously, `navigate` method navigated back if the screen
already exists in the stack. I have seen many people get confused by this
behavior. This behavior is also used for sending params to a previous
screen in the documentation. However, it's also problematic since it could
either push or pop the screens based on the scenario.

This removes the going back behavior from `navigate` and adds a new method
`popTo` to go back to a specific screen in the stack.

The methods now behave as follows:

- `navigate(screenName)` will stay on the current screen if the screen is
already focused, otherwise push a new screen to the stack.
- `popTo(screenName)` will go back to the screen if it exists in the stack,
otherwise pop the current screen and add this screen to the stack.
- To achieve the previous behavior with `navigate`, you can use the `getId`
prop in which case it'll go to the screen with the matching ID and push or
pop screens accordingly.
* Previously, you could specify a route `key` to navigate to, e.g.
`navigation.navigate({ key: 'someuniquekey' })`. It's problematic since `key` is an internal
implementation detail and created by the library internally - which makes it weird to use. None
of the other actions support such usage either.

In addition, we have already added a better API (`getId`) which can be used for similar use
cases - and gives users full control since they provide the ID and it's not autogenerated by the
library.

So this change removes the `key` option from the `navigate` action.

## [6.1.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.1.1...@react-navigation/routers@6.1.3) (2022-09-16)

**Note:** Version bump only for package @react-navigation/routers

## [6.1.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.1.1...@react-navigation/routers@6.1.2) (2022-08-24)

**Note:** Version bump only for package @react-navigation/routers

## [6.1.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.1.0...@react-navigation/routers@6.1.1) (2022-07-05)

**Note:** Version bump only for package @react-navigation/routers

# [6.1.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.2...@react-navigation/routers@6.1.0) (2021-10-12)

### Features

* add a `navigationKey` prop to Screen and Group ([b2fa62c](https://github.com/react-navigation/react-navigation/commit/b2fa62c8ea5c5ad40a3541a7258cba62467e7a56))

## [6.0.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.1...@react-navigation/routers@6.0.2) (2021-10-09)

### Bug Fixes

* properly handle history if drawer is open by default ([de2d4e4](https://github.com/react-navigation/react-navigation/commit/de2d4e4f0659fea87522b918fb09c8f07bdd0697))

## [6.0.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0...@react-navigation/routers@6.0.1) (2021-08-03)

### Reverts

* Revert "fix: don't merge initial params when merge !== true" ([8086772](https://github.com/react-navigation/react-navigation/commit/80867722c5891b786e8c47f18135419b7cb915b3))

# [6.0.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0-next.6...@react-navigation/routers@6.0.0) (2021-08-01)

### Bug Fixes

* don't merge initial params when merge !== true ([54b215b](https://github.com/react-navigation/react-navigation/commit/54b215b9d3192d11c4c28bd469dd217d90d6c5c5))

# [6.0.0-next.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0-next.4...@react-navigation/routers@6.0.0-next.6) (2021-07-01)

**Note:** Version bump only for package @react-navigation/routers

# [6.0.0-next.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0-next.4...@react-navigation/routers@6.0.0-next.5) (2021-06-10)

**Note:** Version bump only for package @react-navigation/routers

# [6.0.0-next.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0-next.3...@react-navigation/routers@6.0.0-next.4) (2021-05-27)

**Note:** Version bump only for package @react-navigation/routers

# [6.0.0-next.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0-next.2...@react-navigation/routers@6.0.0-next.3) (2021-05-23)

**Note:** Version bump only for package @react-navigation/routers

# [6.0.0-next.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0-next.1...@react-navigation/routers@6.0.0-next.2) (2021-04-08)

**Note:** Version bump only for package @react-navigation/routers

# [6.0.0-next.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@6.0.0...@react-navigation/routers@6.0.0-next.1) (2021-03-10)

**Note:** Version bump only for package @react-navigation/routers

# [6.0.0-next.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.6.2...@react-navigation/routers@6.0.0-next.0) (2021-03-09)

### Bug Fixes

* default to backBehavior: firstRoute for TabRouter ([8bdc6c6](https://github.com/react-navigation/react-navigation/commit/8bdc6c6b9bc957a00a01eec2fcf6f971998c9380))
* don't merge params on navigation ([366d018](https://github.com/react-navigation/react-navigation/commit/366d0181dc02597b8d11e343f37fc77bee164c70))
* fix getId being called for incorrect routes. closes [#9343](https://github.com/react-navigation/react-navigation/issues/9343) ([61e653d](https://github.com/react-navigation/react-navigation/commit/61e653d7c484e8ff09045e8635cce4f566a141b4))
* fix StackRouter incorrectly handling invalid route if key is present ([3367ddf](https://github.com/react-navigation/react-navigation/commit/3367ddf9df5696c2b596deee7342040b8ea50763))

### Code Refactoring

* don't use a boolean for drawer status ([cda6397](https://github.com/react-navigation/react-navigation/commit/cda6397b8989c552824eca4175577527c9d72f93))

### Features

* add a new backBehavior: firstRoute for TabRouter ([7c1cd26](https://github.com/react-navigation/react-navigation/commit/7c1cd261bfe70f14294daa598a5c72c777c911d3))
* add a way to specify an unique ID for screens ([15b8bb3](https://github.com/react-navigation/react-navigation/commit/15b8bb34584db3cb166f6aafd45f0b95f14fde62))
* add pressColor and pressOpacity props to drawerItem ([#8834](https://github.com/react-navigation/react-navigation/issues/8834)) ([52dbe4b](https://github.com/react-navigation/react-navigation/commit/52dbe4bd6663430745b07ea379d44d4d4f2944a0))
* associate path with the route it opens when deep linking ([#9384](https://github.com/react-navigation/react-navigation/issues/9384)) ([86e64fd](https://github.com/react-navigation/react-navigation/commit/86e64fdcd81a57cf3f3bdab4c9035b52984e7009)), closes [#9102](https://github.com/react-navigation/react-navigation/issues/9102)

### BREAKING CHANGES

* Drawer status is now a union ('open', 'closed') instead of a boolean. This will let us implement more types of status in future.

Following this the following exports have been renamed as well:
- getIsDrawerOpenFromState -> getDrawerStatusFromState
- useIsDrawerOpen -> useDrawerStatus
* Returning to first route after pressing back seems more common in apps. This commit changes the default for tab and drawer navigators to follow this common practice. To preserve previous behavior, you can pass backBehavior=history to tab and drawer navigators.
* Previous versions of React Navigation merged params on navigation which caused confusion. This commit changes params not to be merged.

The old behaviour can still be achieved by passing `merge: true` explicitly:

```js
CommonActions.navigate({
  name: 'bar',
  params: { fruit: 'orange' },
  merge: true,
})
```
`initialParams` specified for the screen are always merged.

## [5.6.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.6.1...@react-navigation/routers@5.6.2) (2020-11-09)

**Note:** Version bump only for package @react-navigation/routers

## [5.6.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.6.0...@react-navigation/routers@5.6.1) (2020-11-08)

**Note:** Version bump only for package @react-navigation/routers

# [5.6.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.5.1...@react-navigation/routers@5.6.0) (2020-11-04)

### Features

* add a merge option to navigate to control merging params ([9beca3a](https://github.com/react-navigation/react-navigation/commit/9beca3a8027c6e2135dbef2abb8eede6b0b4bc44))

## [5.5.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.5.0...@react-navigation/routers@5.5.1) (2020-10-28)

### Bug Fixes

* improve types for route prop in screenOptions ([d26bcc0](https://github.com/react-navigation/react-navigation/commit/d26bcc057ef31f8950f909adf83e263171a42d74))

# [5.5.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.4.12...@react-navigation/routers@5.5.0) (2020-10-24)

### Bug Fixes

* handle pushing a route with duplicate key ([091b2a2](https://github.com/react-navigation/react-navigation/commit/091b2a2038af1097be57a1bb451623d64a1efc92))

### Features

* allow deep linking to reset state ([#8973](https://github.com/react-navigation/react-navigation/issues/8973)) ([7f3b27a](https://github.com/react-navigation/react-navigation/commit/7f3b27a9ec8edd9604ac19774baa1f60963ccdc9)), closes [#8952](https://github.com/react-navigation/react-navigation/issues/8952)
* improve types for navigation state ([#8980](https://github.com/react-navigation/react-navigation/issues/8980)) ([7dc2f58](https://github.com/react-navigation/react-navigation/commit/7dc2f5832e371473f3263c01ab39824eb9e2057d))

## [5.4.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.4.11...@react-navigation/routers@5.4.12) (2020-09-22)

**Note:** Version bump only for package @react-navigation/routers

## [5.4.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.4.10...@react-navigation/routers@5.4.11) (2020-08-04)

**Note:** Version bump only for package @react-navigation/routers

## [5.4.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.4.9...@react-navigation/routers@5.4.10) (2020-07-28)

### Bug Fixes

* make sure history is correct after rehydration ([b70e3fe](https://github.com/react-navigation/react-navigation/commit/b70e3fe61852502322b2cb46c5934800462b0267))
* make sure index is correct when rehydrating state for tabs ([#8638](https://github.com/react-navigation/react-navigation/issues/8638)) ([1aa8219](https://github.com/react-navigation/react-navigation/commit/1aa8219021f6c231a3e150fc9bea73f12542f85c))

## [5.4.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.4.8...@react-navigation/routers@5.4.9) (2020-07-10)

### Bug Fixes

* mark some types as read-only ([7c3a0a0](https://github.com/react-navigation/react-navigation/commit/7c3a0a0f23629da0beb956ba5a9689ab965061ce))
* only remove non-existed routes from tab history. closes [#8567](https://github.com/react-navigation/react-navigation/issues/8567) ([374b081](https://github.com/react-navigation/react-navigation/commit/374b081b1c4b2e590259a050430eb1fcdbad3557))

## [5.4.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/routers@5.4.7...@react-navigation/routers@5.4.8) (2020-06-24)

### Bug Fixes

* more improvements to types ([d244488](https://github.com/react-navigation/react-navigation/commit/d2444887be227bbbdcfcb13a7f26a8ebb344043e))

## [5.4.7](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.6...@react-navigation/routers@5.4.7) (2020-05-23)

**Note:** Version bump only for package @react-navigation/routers

## [5.4.6](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.5...@react-navigation/routers@5.4.6) (2020-05-20)

**Note:** Version bump only for package @react-navigation/routers

## [5.4.5](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.4...@react-navigation/routers@5.4.5) (2020-05-20)

**Note:** Version bump only for package @react-navigation/routers

## [5.4.4](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.3...@react-navigation/routers@5.4.4) (2020-05-08)

### Bug Fixes

* fix building typescript definitions. closes [#8216](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/issues/8216) ([47a1229](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/47a12298378747edd2d22e54dc1c8677f98c49b4))

## [5.4.3](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.2...@react-navigation/routers@5.4.3) (2020-05-08)

**Note:** Version bump only for package @react-navigation/routers

## [5.4.2](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.1...@react-navigation/routers@5.4.2) (2020-04-30)

### Bug Fixes

* fix backBehavior with initialRoute ([#8110](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/issues/8110)) ([420f692](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/420f6926e111d32c2388c44ff0bee2b8ea238a57))

## [5.4.1](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.4.0...@react-navigation/routers@5.4.1) (2020-04-27)

### Bug Fixes

* fix behaviour of openByDefault in drawer when focus changes ([b172b51](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/b172b51f175a9f8044cb2a8e9d74a86480d8f11e))

# [5.4.0](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.3.0...@react-navigation/routers@5.4.0) (2020-04-17)

### Features

* add openByDefault option to drawer ([36689e2](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/36689e24c21b474692bb7ecd0b901c8afbbe9a20))

# [5.3.0](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.2.1...@react-navigation/routers@5.3.0) (2020-04-08)

### Bug Fixes

* separate normal exports and type exports ([303f0b7](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/303f0b78a5ab717b2d606cd9c8a22f3dae051f0f))

### Features

* make replace bubble up ([ba1f405](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/ba1f4051299ad86001592b8d3601c16fece159df))

## [5.2.1](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.2.0...@react-navigation/routers@5.2.1) (2020-03-30)

**Note:** Version bump only for package @react-navigation/routers

# [5.2.0](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.1.1...@react-navigation/routers@5.2.0) (2020-03-22)

### Features

* add keys to routes missing keys during reset ([813a590](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/813a5903b5f44506b9097538ed85229e565b855e))

## [5.1.1](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.1.0...@react-navigation/routers@5.1.1) (2020-03-16)

### Bug Fixes

* don't handle action if no routes are present ([660cac3](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/660cac3557bce8978812ce2750e961e7ada92d13))

# [5.1.0](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.3...@react-navigation/routers@5.1.0) (2020-03-03)

### Bug Fixes

* fix links for documentation ([5bb0f40](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/5bb0f405ceb5755d39a0b5b1f2e4ecee0da051bc))

### Features

* make reset bubble up ([09f6808](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/09f6808d7d43c70b2c502151f9f20fad03972886))

## [5.0.3](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.2...@react-navigation/routers@5.0.3) (2020-02-26)

**Note:** Version bump only for package @react-navigation/routers

## [5.0.2](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.1...@react-navigation/routers@5.0.2) (2020-02-21)

### Bug Fixes

* tweak error message for navigate ([c8ea419](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/c8ea4199f4b19a58d5e409cfcc96e587fe354a9a))

## [5.0.1](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.33...@react-navigation/routers@5.0.1) (2020-02-10)

### Bug Fixes

* merge initial params on replace ([80629bf](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/80629bf30baf8f17620e6d3127e00376182af074))

# [5.0.0-alpha.33](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.32...@react-navigation/routers@5.0.0-alpha.33) (2020-02-04)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.32](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.31...@react-navigation/routers@5.0.0-alpha.32) (2020-02-04)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.31](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.30...@react-navigation/routers@5.0.0-alpha.31) (2020-02-03)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.30](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.27...@react-navigation/routers@5.0.0-alpha.30) (2020-02-02)

### Bug Fixes

* add licenses ([0c159db](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/0c159db4c9bc85e83b5cfe6819ab2562669a4d8f))

# [5.0.0-alpha.28](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.27...@react-navigation/routers@5.0.0-alpha.28) (2020-02-02)

### Bug Fixes

* add licenses ([0c159db](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/0c159db4c9bc85e83b5cfe6819ab2562669a4d8f))

# [5.0.0-alpha.27](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.26...@react-navigation/routers@5.0.0-alpha.27) (2020-01-24)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.26](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.25...@react-navigation/routers@5.0.0-alpha.26) (2020-01-23)

### Bug Fixes

* handle popping more than available screens in stack ([68ed8a7](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/68ed8a725950f39228847ab10b3dd7f3ebd2e2dc))

# [5.0.0-alpha.25](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.24...@react-navigation/routers@5.0.0-alpha.25) (2020-01-14)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.24](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.23...@react-navigation/routers@5.0.0-alpha.24) (2020-01-13)

### Bug Fixes

* make sure paths aren't aliased when building definitions ([65a5dac](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/65a5dac2bf887f4ba081ab15bd4c9870bb15697f)), closes [#265](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/issues/265)

# [5.0.0-alpha.23](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.22...@react-navigation/routers@5.0.0-alpha.23) (2020-01-13)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.22](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.20...@react-navigation/routers@5.0.0-alpha.22) (2020-01-09)

### Bug Fixes

* change POP behaviour to remove elements from index only ([7a3d652](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/7a3d652e847e173964a06cc9d859129ca0317861)), closes [#256](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/issues/256)

# [5.0.0-alpha.21](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/compare/@react-navigation/routers@5.0.0-alpha.20...@react-navigation/routers@5.0.0-alpha.21) (2020-01-09)

### Bug Fixes

* change POP behaviour to remove elements from index only ([7a3d652](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/commit/7a3d652e847e173964a06cc9d859129ca0317861)), closes [#256](https://github.com/react-navigation/react-navigation/tree/main/packages/routers/issues/256)

# [5.0.0-alpha.20](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.19...@react-navigation/routers@5.0.0-alpha.20) (2020-01-05)

### Bug Fixes

* preserve focused route in tab on changing screens list ([adbeb29](https://github.com/react-navigation/navigation-ex/commit/adbeb292f522be8d7a58dd3f84e560a6d83d01a8))

# [5.0.0-alpha.19](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.18...@react-navigation/routers@5.0.0-alpha.19) (2020-01-01)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.18](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.17...@react-navigation/routers@5.0.0-alpha.18) (2019-12-19)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.17](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.16...@react-navigation/routers@5.0.0-alpha.17) (2019-12-16)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.16](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.15...@react-navigation/routers@5.0.0-alpha.16) (2019-12-11)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.15](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.14...@react-navigation/routers@5.0.0-alpha.15) (2019-11-17)

### Bug Fixes

* merge initial params on push ([11efb06](https://github.com/react-navigation/navigation-ex/commit/11efb066429a3fc8b7e8e48d897286208d9a5449))

# [5.0.0-alpha.14](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.13...@react-navigation/routers@5.0.0-alpha.14) (2019-11-10)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.13](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.12...@react-navigation/routers@5.0.0-alpha.13) (2019-11-08)

### Bug Fixes

* handle invalid initialRouteName gracefully ([b5d9ad9](https://github.com/react-navigation/navigation-ex/commit/b5d9ad9))

# [5.0.0-alpha.12](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.11...@react-navigation/routers@5.0.0-alpha.12) (2019-11-04)

### Bug Fixes

* close drawer on back button press ([3a4c38b](https://github.com/react-navigation/navigation-ex/commit/3a4c38b))

# [5.0.0-alpha.11](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.10...@react-navigation/routers@5.0.0-alpha.11) (2019-10-30)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.10](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.9...@react-navigation/routers@5.0.0-alpha.10) (2019-10-29)

### Bug Fixes

* use index of first route when rehydrating tab state ([7635373](https://github.com/react-navigation/navigation-ex/commit/7635373))

# [5.0.0-alpha.9](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.8...@react-navigation/routers@5.0.0-alpha.9) (2019-10-03)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.8](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.7...@react-navigation/routers@5.0.0-alpha.8) (2019-09-27)

### Bug Fixes

* close drawer on navigate ([655a220](https://github.com/react-navigation/navigation-ex/commit/655a220))

# [5.0.0-alpha.7](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.6...@react-navigation/routers@5.0.0-alpha.7) (2019-08-31)

### Bug Fixes

* handle route names change when all routes are removed ([#86](https://github.com/react-navigation/navigation-ex/issues/86)) ([1b2983e](https://github.com/react-navigation/navigation-ex/commit/1b2983e))

# [5.0.0-alpha.6](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.5...@react-navigation/routers@5.0.0-alpha.6) (2019-08-29)

### Features

* handle navigating with both with both key and name ([#83](https://github.com/react-navigation/navigation-ex/issues/83)) ([6b75cba](https://github.com/react-navigation/navigation-ex/commit/6b75cba))

# [5.0.0-alpha.5](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.4...@react-navigation/routers@5.0.0-alpha.5) (2019-08-28)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.4](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.3...@react-navigation/routers@5.0.0-alpha.4) (2019-08-27)

**Note:** Version bump only for package @react-navigation/routers

# [5.0.0-alpha.3](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.2...@react-navigation/routers@5.0.0-alpha.3) (2019-08-22)

### Bug Fixes

* fix path to typescript definitions ([f182315](https://github.com/react-navigation/navigation-ex/commit/f182315))

# [5.0.0-alpha.2](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/routers@5.0.0-alpha.1...@react-navigation/routers@5.0.0-alpha.2) (2019-08-22)

### Bug Fixes

* properly handle pop action from stack ([61dce7a](https://github.com/react-navigation/navigation-ex/commit/61dce7a))

# 5.0.0-alpha.1 (2019-08-21)

### Bug Fixes

* don't lose child state when rehydrating in tab router ([5676dea](https://github.com/react-navigation/navigation-ex/commit/5676dea))
* don't use action.source for stack router ([afa24c1](https://github.com/react-navigation/navigation-ex/commit/afa24c1))
* fix peer deps and add git urls ([6b4fc74](https://github.com/react-navigation/navigation-ex/commit/6b4fc74))
* handle partial initial state better when rehydrating ([8ed54da](https://github.com/react-navigation/navigation-ex/commit/8ed54da))
* implement canGoBack for tab router ([#51](https://github.com/react-navigation/navigation-ex/issues/51)) ([2b8f2ed](https://github.com/react-navigation/navigation-ex/commit/2b8f2ed))

### Features

* add a simple stack and material tabs integration ([#39](https://github.com/react-navigation/navigation-ex/issues/39)) ([e0bee10](https://github.com/react-navigation/navigation-ex/commit/e0bee10))
* add a target key to actions and various fixes ([747ce66](https://github.com/react-navigation/navigation-ex/commit/747ce66))
* add canGoBack ([#50](https://github.com/react-navigation/navigation-ex/issues/50)) ([e9da86e](https://github.com/react-navigation/navigation-ex/commit/e9da86e))
* add drawer navigator integration ([#43](https://github.com/react-navigation/navigation-ex/issues/43)) ([d02277b](https://github.com/react-navigation/navigation-ex/commit/d02277b))
* add hook for deep link support ([35987ae](https://github.com/react-navigation/navigation-ex/commit/35987ae))
* integrate reanimated based stack ([#42](https://github.com/react-navigation/navigation-ex/issues/42)) ([dcf57c0](https://github.com/react-navigation/navigation-ex/commit/dcf57c0))
