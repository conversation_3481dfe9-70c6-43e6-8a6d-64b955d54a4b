/**
 * Navigators
 */
export { createMaterialTopTabNavigator } from './navigators/createMaterialTopTabNavigator';

/**
 * Views
 */
export { MaterialTopTabBar } from './views/MaterialTopTabBar';
export { MaterialTopTabView } from './views/MaterialTopTabView';

/**
 * Utilities
 */
export { useTabAnimation } from './utils/useTabAnimation';

/**
 * Types
 */
export type {
  MaterialTopTabBarProps,
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  MaterialTopTabNavigationProp,
  MaterialTopTabNavigatorProps,
  MaterialTopTabOptionsArgs,
  MaterialTopTabScreenProps,
} from './types';
