# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [7.2.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.2.0...@react-navigation/bottom-tabs@7.2.1) (2025-03-02)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.2.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.1.3...@react-navigation/bottom-tabs@7.2.0) (2024-12-12)

### Bug Fixes

* don't freeze active and preloaded screens ([#12332](https://github.com/react-navigation/react-navigation/issues/12332)) ([015d94d](https://github.com/react-navigation/react-navigation/commit/015d94d61e8631c6f4d5471ca3c3372fe477e930)) - by @WoLewicki

### Features

* export *NavigatorProps for each navigator ([#12327](https://github.com/react-navigation/react-navigation/issues/12327)) ([316e2ff](https://github.com/react-navigation/react-navigation/commit/316e2ff7126c2c1e38ddd7296342a07155f78817)) - by @marklawlor

## [7.1.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.1.2...@react-navigation/bottom-tabs@7.1.3) (2024-12-02)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.1.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.1.1...@react-navigation/bottom-tabs@7.1.2) (2024-12-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.1.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.1.0...@react-navigation/bottom-tabs@7.1.1) (2024-12-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.1.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.14...@react-navigation/bottom-tabs@7.1.0) (2024-12-01)

### Features

* show large content title on iOS for better accessibility ([#12290](https://github.com/react-navigation/react-navigation/issues/12290)) ([1924f07](https://github.com/react-navigation/react-navigation/commit/1924f07f91191c482671b1a1a61d0a267f86077d)), closes [facebook/react-native#45903](https://github.com/facebook/react-native/issues/45903) - by @bacarybruno

## [7.0.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.13...@react-navigation/bottom-tabs@7.0.14) (2024-11-28)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.12...@react-navigation/bottom-tabs@7.0.13) (2024-11-27)

### Bug Fixes

* use a narrower type for tabBarButton ([ec4b764](https://github.com/react-navigation/react-navigation/commit/ec4b7640c4aab9b0dbd691be69cf92e1c7039800)), closes [#12304](https://github.com/react-navigation/react-navigation/issues/12304) - by @satya164

## [7.0.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.11...@react-navigation/bottom-tabs@7.0.12) (2024-11-26)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.10...@react-navigation/bottom-tabs@7.0.11) (2024-11-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.9...@react-navigation/bottom-tabs@7.0.10) (2024-11-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.8...@react-navigation/bottom-tabs@7.0.9) (2024-11-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.7...@react-navigation/bottom-tabs@7.0.8) (2024-11-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.6...@react-navigation/bottom-tabs@7.0.7) (2024-11-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.5...@react-navigation/bottom-tabs@7.0.6) (2024-11-19)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.4...@react-navigation/bottom-tabs@7.0.5) (2024-11-18)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.3...@react-navigation/bottom-tabs@7.0.4) (2024-11-15)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.2...@react-navigation/bottom-tabs@7.0.3) (2024-11-14)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.1...@react-navigation/bottom-tabs@7.0.2) (2024-11-13)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [7.0.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0...@react-navigation/bottom-tabs@7.0.1) (2024-11-10)

### Bug Fixes

* add missing screenLayout prop to the bottom tab navigator ([#12246](https://github.com/react-navigation/react-navigation/issues/12246)) ([e5a8071](https://github.com/react-navigation/react-navigation/commit/e5a80714daa8543dfe9a98a5217f007adaf95f94)) - by @dimonnwc3

# [7.0.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.37...@react-navigation/bottom-tabs@7.0.0) (2024-11-06)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.37](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.36...@react-navigation/bottom-tabs@7.0.0-rc.37) (2024-10-31)

### Code Refactoring

* rename sceneContainerStyle to sceneStyle ([d1d0761](https://github.com/react-navigation/react-navigation/commit/d1d0761f0239caea1cc7b85d90de229f444f827d)) - by @satya164

### BREAKING CHANGES

* This does the following changes:

- Remove the `sceneContainerStyle` prop from Bottom Tabs & Material Top Tabs
- Add a `sceneStyle` option to Bottom Tabs & Material Top Tabs
- Rename `sceneContainerStyle` option to `sceneStyle` for Drawer

# [7.0.0-rc.36](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.35...@react-navigation/bottom-tabs@7.0.0-rc.36) (2024-10-29)

### Bug Fixes

* bump peer dep version requirement for screens ([63f1687](https://github.com/react-navigation/react-navigation/commit/63f16871c4db0c275c2d393f668adec45d31ac7a)) - by @satya164

# [7.0.0-rc.35](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.34...@react-navigation/bottom-tabs@7.0.0-rc.35) (2024-10-27)

### Features

* add transition events to BottomTabs ([#12207](https://github.com/react-navigation/react-navigation/issues/12207)) ([bc4e50c](https://github.com/react-navigation/react-navigation/commit/bc4e50cfb9fa78c39ed2871a5465c2cc97e73b31)) - by @satya164

# [7.0.0-rc.34](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.33...@react-navigation/bottom-tabs@7.0.0-rc.34) (2024-10-24)

### Bug Fixes

* use * for react-native peer dep to support pre-release versions ([07267e5](https://github.com/react-navigation/react-navigation/commit/07267e54be752f600f808ec2898e5d76a1bc1d43)) - by @satya164

# [7.0.0-rc.33](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.32...@react-navigation/bottom-tabs@7.0.0-rc.33) (2024-10-11)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.32](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.31...@react-navigation/bottom-tabs@7.0.0-rc.32) (2024-09-10)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.31](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.30...@react-navigation/bottom-tabs@7.0.0-rc.31) (2024-09-08)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.30](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.29...@react-navigation/bottom-tabs@7.0.0-rc.30) (2024-08-13)

### Bug Fixes

* fix detecting animation in bottom tabs ([d24496b](https://github.com/react-navigation/react-navigation/commit/d24496beac90862ea6d91329bfec42aa5dd40076)) - by @satya164

# [7.0.0-rc.29](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.28...@react-navigation/bottom-tabs@7.0.0-rc.29) (2024-08-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.28](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.27...@react-navigation/bottom-tabs@7.0.0-rc.28) (2024-08-08)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.27](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.26...@react-navigation/bottom-tabs@7.0.0-rc.27) (2024-08-07)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.26](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.25...@react-navigation/bottom-tabs@7.0.0-rc.26) (2024-08-05)

### Features

* add headerBackButtonDisplayMode for native stack ([#12089](https://github.com/react-navigation/react-navigation/issues/12089)) ([89ffa1b](https://github.com/react-navigation/react-navigation/commit/89ffa1baa1dc3ad8260361a3f84aa21d24c1643e)), closes [#11980](https://github.com/react-navigation/react-navigation/issues/11980) - by @dylancom

### BREAKING CHANGES

* This removes the `headerBackTitleVisible` option,

Adds corresponding functionality from
https://github.com/software-mansion/react-native-screens/pull/2123.

# [7.0.0-rc.25](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.24...@react-navigation/bottom-tabs@7.0.0-rc.25) (2024-08-02)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.24](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.23...@react-navigation/bottom-tabs@7.0.0-rc.24) (2024-08-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.23](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.22...@react-navigation/bottom-tabs@7.0.0-rc.23) (2024-07-25)

### Bug Fixes

* fix type inference for params. closes [#12071](https://github.com/react-navigation/react-navigation/issues/12071) ([3299b70](https://github.com/react-navigation/react-navigation/commit/3299b70682adbf55811369535cca1cdd0dc59860)) - by @

# [7.0.0-rc.22](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.21...@react-navigation/bottom-tabs@7.0.0-rc.22) (2024-07-19)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.21](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.20...@react-navigation/bottom-tabs@7.0.0-rc.21) (2024-07-12)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.20](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.19...@react-navigation/bottom-tabs@7.0.0-rc.20) (2024-07-12)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.19](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.18...@react-navigation/bottom-tabs@7.0.0-rc.19) (2024-07-11)

### Bug Fixes

* upgrade react-native-builder-bob ([1575287](https://github.com/react-navigation/react-navigation/commit/1575287d40fadb97f33eb19c2914d8be3066b47a)) - by @

# [7.0.0-rc.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.17...@react-navigation/bottom-tabs@7.0.0-rc.18) (2024-07-11)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.16...@react-navigation/bottom-tabs@7.0.0-rc.17) (2024-07-10)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.15...@react-navigation/bottom-tabs@7.0.0-rc.16) (2024-07-08)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.14...@react-navigation/bottom-tabs@7.0.0-rc.15) (2024-07-07)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.13...@react-navigation/bottom-tabs@7.0.0-rc.14) (2024-07-04)

### Bug Fixes

* fix published files ([829caa0](https://github.com/react-navigation/react-navigation/commit/829caa019e125811eea5213fd380e8e1bdbe7030)) - by @

# [7.0.0-rc.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.12...@react-navigation/bottom-tabs@7.0.0-rc.13) (2024-07-04)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.11...@react-navigation/bottom-tabs@7.0.0-rc.12) (2024-07-04)

### Features

* add package.json exports field ([1435cfe](https://github.com/react-navigation/react-navigation/commit/1435cfe3300767c221ebd4613479ad662d61efee)) - by @

# [7.0.0-rc.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.10...@react-navigation/bottom-tabs@7.0.0-rc.11) (2024-07-03)

### Bug Fixes

* adjust tab bar colors ([dafef7a](https://github.com/react-navigation/react-navigation/commit/dafef7ac0f5fc133eaa152b150d1755f7f115c04)) - by @satya164

# [7.0.0-rc.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.9...@react-navigation/bottom-tabs@7.0.0-rc.10) (2024-07-03)

### Features

* add a tabBarVariant option ([#12045](https://github.com/react-navigation/react-navigation/issues/12045)) ([9e43db5](https://github.com/react-navigation/react-navigation/commit/9e43db5e888489a283babfdbfe8cda77aba1b88d)) - by @satya164

# [7.0.0-rc.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.8...@react-navigation/bottom-tabs@7.0.0-rc.9) (2024-07-03)

### Bug Fixes

* fix tab bar styling for compact, material, ipad etc. ([2e7c377](https://github.com/react-navigation/react-navigation/commit/2e7c37791947d765ed0a9cddff51cbf1fdb1f2f2)) - by @

# [7.0.0-rc.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.7...@react-navigation/bottom-tabs@7.0.0-rc.8) (2024-07-02)

### Bug Fixes

* drop leftover empty string for headerBackTitleVisible ([b93f861](https://github.com/react-navigation/react-navigation/commit/b93f86155fe9185c5197cd6d44b625aabb8ca4a7)) - by @satya164

# [7.0.0-rc.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.6...@react-navigation/bottom-tabs@7.0.0-rc.7) (2024-07-01)

### Bug Fixes

* stop using react-native field in package.json ([efc33cb](https://github.com/react-navigation/react-navigation/commit/efc33cb0c4830a84ceae034dc1278c54f1faf32d)) - by @

# [7.0.0-rc.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.5...@react-navigation/bottom-tabs@7.0.0-rc.6) (2024-06-29)

### Bug Fixes

* add a workaround for incorrect inference [#12041](https://github.com/react-navigation/react-navigation/issues/12041) ([85c4bbb](https://github.com/react-navigation/react-navigation/commit/85c4bbbf535cde2ba9cd537a2a5ce34f060d32b9)) - by @

# [7.0.0-rc.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.4...@react-navigation/bottom-tabs@7.0.0-rc.5) (2024-06-28)

### Bug Fixes

* add hover effect to drawer and tab sidebar on web ([fa2280d](https://github.com/react-navigation/react-navigation/commit/fa2280d3633cfdb492f9cdb06d8dd331d3e34c71)) - by @
* fix ripple effect overflow in tab bar on Android ([5ae1047](https://github.com/react-navigation/react-navigation/commit/5ae1047114724705f8026db6be0f0452e92e89e0)) - by @satya164

# [7.0.0-rc.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.3...@react-navigation/bottom-tabs@7.0.0-rc.4) (2024-06-28)

### Bug Fixes

* tweak spacing for drawer & bottom tabs sidebar ([e33f78f](https://github.com/react-navigation/react-navigation/commit/e33f78faf3ecc3ff84001f56fcbd5cdb1c7cb164)) - by @

# [7.0.0-rc.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.2...@react-navigation/bottom-tabs@7.0.0-rc.3) (2024-06-28)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.1...@react-navigation/bottom-tabs@7.0.0-rc.2) (2024-06-28)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-rc.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-rc.0...@react-navigation/bottom-tabs@7.0.0-rc.1) (2024-06-27)

### Bug Fixes

* fix bottom tab bar layout ([054e1dc](https://github.com/react-navigation/react-navigation/commit/054e1dcfa94368ef8b22cf45b96c907c1d3fb573)) - by @

# [7.0.0-rc.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.22...@react-navigation/bottom-tabs@7.0.0-rc.0) (2024-06-27)

### Bug Fixes

* add hover effect to buttons on iPad & VisionOS ([2cb77c0](https://github.com/react-navigation/react-navigation/commit/2cb77c0ce42575275dd723555d0ec9ae7be32c66)) - by @satya164
* adjust bottom tabs sidebar for rtl ([480ab04](https://github.com/react-navigation/react-navigation/commit/480ab045906eb455f12182372f20be7584e0c327)) - by @satya164
* adjust sidebar styling in bottom tabs to match drawer ([#12008](https://github.com/react-navigation/react-navigation/issues/12008)) ([a1e0499](https://github.com/react-navigation/react-navigation/commit/a1e049958cbcb157ffaf4fa80976cb30b0567ba9)) - by @Piotrfj
* move sidebar paddings to the wrapper view so it's customizable. closes [#11927](https://github.com/react-navigation/react-navigation/issues/11927) ([4761440](https://github.com/react-navigation/react-navigation/commit/4761440146e1c3381390822b167d319e0a4abb7e)) - by @satya164
* use useSafeAreaFrame in bottom tabs as we now have a workaround ([92dc079](https://github.com/react-navigation/react-navigation/commit/92dc07962495f2765676613d841e61ff3d1685e2)) - by @satya164

# [7.0.0-alpha.22](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.21...@react-navigation/bottom-tabs@7.0.0-alpha.22) (2024-03-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.21](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.20...@react-navigation/bottom-tabs@7.0.0-alpha.21) (2024-03-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.20](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.19...@react-navigation/bottom-tabs@7.0.0-alpha.20) (2024-03-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.19](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.18...@react-navigation/bottom-tabs@7.0.0-alpha.19) (2024-03-20)

### Features

* add getStateForRouteNamesChange to all navigators and mark it as unstable ([4edbb07](https://github.com/react-navigation/react-navigation/commit/4edbb071163742b60499178271fd3e3e92fb4002)) - by @satya164

# [7.0.0-alpha.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.17...@react-navigation/bottom-tabs@7.0.0-alpha.18) (2024-03-14)

### Features

* automatically infer types for navigation in options, listeners etc. ([#11883](https://github.com/react-navigation/react-navigation/issues/11883)) ([c54baf1](https://github.com/react-navigation/react-navigation/commit/c54baf14640e567be10cb8a5f68e5cbf0b35f120)) - by @satya164

# [7.0.0-alpha.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.16...@react-navigation/bottom-tabs@7.0.0-alpha.17) (2024-03-10)

### Features

* add a type for options arguments ([8e719e0](https://github.com/react-navigation/react-navigation/commit/8e719e0faefbd1eed9f7122a3d8e2c617d5f8254)) - by @satya164

# [7.0.0-alpha.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.15...@react-navigation/bottom-tabs@7.0.0-alpha.16) (2024-03-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.14...@react-navigation/bottom-tabs@7.0.0-alpha.15) (2024-03-08)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.13...@react-navigation/bottom-tabs@7.0.0-alpha.14) (2024-03-08)

### Bug Fixes

* fix flicker for intermediate screens for tab animation ([9d54040](https://github.com/react-navigation/react-navigation/commit/9d540409d0d324027b6196f756cb98de6eb52f1f)) - by @satya164

# [7.0.0-alpha.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.12...@react-navigation/bottom-tabs@7.0.0-alpha.13) (2024-03-08)

### Bug Fixes

* align animation APIs for stack and bottom tabs ([2599bc2](https://github.com/react-navigation/react-navigation/commit/2599bc2cf176118ff910610facd443d2e7232d77)) - by @satya164
* fix typo ([4101318](https://github.com/react-navigation/react-navigation/commit/4101318ac68c239ac6963c35bb045e96938fb62e)) - by @satya164

### Features

* add an animation option to customize animations ([f0b7e6f](https://github.com/react-navigation/react-navigation/commit/f0b7e6f3343b4bc61ebd695accd0965b4d5e34fd)) - by @satya164

# [7.0.0-alpha.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.11...@react-navigation/bottom-tabs@7.0.0-alpha.12) (2024-03-04)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.10...@react-navigation/bottom-tabs@7.0.0-alpha.11) (2024-02-24)

### Bug Fixes

* fix peer dependency versions ([4b93b63](https://github.com/react-navigation/react-navigation/commit/4b93b6335ce180fe879f9fbe8f2400426b5484fb)) - by @

# [7.0.0-alpha.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.9...@react-navigation/bottom-tabs@7.0.0-alpha.10) (2024-02-23)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.8...@react-navigation/bottom-tabs@7.0.0-alpha.9) (2024-02-23)

### Features

* [bottom-tabs] allow position = top ([#11782](https://github.com/react-navigation/react-navigation/issues/11782)) ([aa5ae17](https://github.com/react-navigation/react-navigation/commit/aa5ae177d4d6b7ea2ae36c194b6a46747c242f9b)) - by @douglowder

# [7.0.0-alpha.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.7...@react-navigation/bottom-tabs@7.0.0-alpha.8) (2024-01-17)

### Features

* preloading for simple navigators - tabs, drawer ([#11709](https://github.com/react-navigation/react-navigation/issues/11709)) ([ad7c703](https://github.com/react-navigation/react-navigation/commit/ad7c703f1c0e66d77f0ab235e13fe43ca813ed1d)) - by @osdnk
* preloading in routers  ([382d6e6](https://github.com/react-navigation/react-navigation/commit/382d6e6f3312630b34332b1ae7d4bd7bf9b4ee60)) - by @osdnk

# [7.0.0-alpha.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.6...@react-navigation/bottom-tabs@7.0.0-alpha.7) (2023-11-17)

### Bug Fixes

* update peer dependencies when publishing ([c440703](https://github.com/react-navigation/react-navigation/commit/c44070310f875e488708f2a6c52ffddcea05b0e6)) - by @

# [7.0.0-alpha.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.5...@react-navigation/bottom-tabs@7.0.0-alpha.6) (2023-11-12)

### Bug Fixes

* forShifting bottom-tabs animation ([#11638](https://github.com/react-navigation/react-navigation/issues/11638)) ([27bfc12](https://github.com/react-navigation/react-navigation/commit/27bfc120d2c8be8b205c41917094c7188c0f32a3)) - by @Bowlerr
* pass `tintColor` to `Label` ([#11617](https://github.com/react-navigation/react-navigation/issues/11617)) ([c2d8aac](https://github.com/react-navigation/react-navigation/commit/c2d8aac84d1d3bf631b72392b3bdae80dc10be5c)), closes [#11246](https://github.com/react-navigation/react-navigation/issues/11246) - by @darshan09200

### Features

* add a layout prop for navigators ([#11614](https://github.com/react-navigation/react-navigation/issues/11614)) ([1f51190](https://github.com/react-navigation/react-navigation/commit/1f511904b9437d1451557147e72962859e97b1ae)) - by @satya164

# [7.0.0-alpha.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.4...@react-navigation/bottom-tabs@7.0.0-alpha.5) (2023-09-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.3...@react-navigation/bottom-tabs@7.0.0-alpha.4) (2023-09-13)

### Features

* add option to show tabs on the side ([#11578](https://github.com/react-navigation/react-navigation/issues/11578)) ([cd15fda](https://github.com/react-navigation/react-navigation/commit/cd15fdafe7acc428826bd5106c7ba62c1b5153ca)) - by @satya164

# [7.0.0-alpha.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.2...@react-navigation/bottom-tabs@7.0.0-alpha.3) (2023-09-07)

### Bug Fixes

* adjust fade animation spec for bottom tabs ([ecd0e66](https://github.com/react-navigation/react-navigation/commit/ecd0e66b5eaf19248629aae06734541a48aa5684)) - by @satya164
* Allow to use `PlatformColor` in the theme ([#11570](https://github.com/react-navigation/react-navigation/issues/11570)) ([64734e7](https://github.com/react-navigation/react-navigation/commit/64734e7bc0d7f203d8e5db6abcc9a88157a5f16c)) - by @retyui
* bottom bar animation tied with a screen ([#11572](https://github.com/react-navigation/react-navigation/issues/11572)) ([9ee55bc](https://github.com/react-navigation/react-navigation/commit/9ee55bc0eee9e7257b7dd914ca994c37384877a0)) - by @osdnk

### Features

* add animation prop to bottom tab ([#11323](https://github.com/react-navigation/react-navigation/issues/11323)) ([8d2a6d8](https://github.com/react-navigation/react-navigation/commit/8d2a6d8ef642872d3d506dca483b7474471a040c)) - by @teneeto
* add shifting animation to bottom-tabs and various fixes ([#11581](https://github.com/react-navigation/react-navigation/issues/11581)) ([6d93c2d](https://github.com/react-navigation/react-navigation/commit/6d93c2da661e1991f6e60f25abf137110a005509)) - by @satya164

# [7.0.0-alpha.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.1...@react-navigation/bottom-tabs@7.0.0-alpha.2) (2023-06-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [7.0.0-alpha.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@7.0.0-alpha.0...@react-navigation/bottom-tabs@7.0.0-alpha.1) (2023-03-01)

### Bug Fixes

* fix paths in sourcemap files ([368e069](https://github.com/react-navigation/react-navigation/commit/368e0691b9fb07d4b1cbe71cfe4c2f40512f93ad)) - by @satya164

### Features

* add ability to customize the fonts with the theme ([#11243](https://github.com/react-navigation/react-navigation/issues/11243)) ([1cd6836](https://github.com/react-navigation/react-navigation/commit/1cd6836f1d10bcdf7f96d9e4b9f7de0ddea9391f)) - by @satya164

# [7.0.0-alpha.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.4.1...@react-navigation/bottom-tabs@7.0.0-alpha.0) (2023-02-17)

* refactor!: improve the API for Link component ([7f35837](https://github.com/react-navigation/react-navigation/commit/7f3583793ad17475531e155f1f433ffa16547015)) - by @satya164

### Features

* expose the original label in children prop for custom label functions in tab navigators ([a6fd49f](https://github.com/react-navigation/react-navigation/commit/a6fd49f9af9353cf5fd5364feafcbeea25c3ff7f)) - by @satya164

### BREAKING CHANGES

* Initially the `Link` component was designed to work with path strings via the `to` prop. But it has few issues:

- The path strings are not type-safe, making it easy to cause typos and bugs after
refactor
- The API made navigating via screen name more incovenient, even if that's the preferred approach

This revamps the API of the `Link` component to make it easier to use. Instead of `to` prop, it now accepts `screen` and `params` props, as well as an optional `href` prop to
use instead of the generated path.

e.g.:

```js
<Link screen="Details" params={{ foo: 42 }}>Go to Details</Link>
```

This also drops the `useLinkTo` hook and consolidates into the `useLinkTools` hook - which lets us build a `href` for a screen or action for a path.

## [6.4.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.4.0...@react-navigation/bottom-tabs@6.4.1) (2022-11-21)

### Bug Fixes

* add accessibility props to NativeStack screens ([#11022](https://github.com/react-navigation/react-navigation/issues/11022)) ([3ab05af](https://github.com/react-navigation/react-navigation/commit/3ab05afeb6412b8e5566270442ac14a463136620))

# [6.4.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.3.2...@react-navigation/bottom-tabs@6.4.0) (2022-09-16)

### Features

* add freezeOnBlur prop  ([#10834](https://github.com/react-navigation/react-navigation/issues/10834)) ([e13b4d9](https://github.com/react-navigation/react-navigation/commit/e13b4d9341362512ba4bf921a17552f3be8735c1))

## [6.3.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.3.2...@react-navigation/bottom-tabs@6.3.3) (2022-08-24)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [6.3.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.3.1...@react-navigation/bottom-tabs@6.3.2) (2022-07-05)

### Bug Fixes

* ensure same @types/react version in repo ([#10663](https://github.com/react-navigation/react-navigation/issues/10663)) ([e662465](https://github.com/react-navigation/react-navigation/commit/e6624653fbbd931158dbebd17142abf9637205b6)), closes [#10655](https://github.com/react-navigation/react-navigation/issues/10655)

## [6.3.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.3.0...@react-navigation/bottom-tabs@6.3.1) (2022-04-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.3.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.2.0...@react-navigation/bottom-tabs@6.3.0) (2022-04-01)

### Features

* add an ID prop to navigators ([4e4935a](https://github.com/react-navigation/react-navigation/commit/4e4935ac2584bc1a00209609cc026fa73e12c10a))

# [6.2.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.1.0...@react-navigation/bottom-tabs@6.2.0) (2022-02-02)

### Features

* add prop for new container ([#9772](https://github.com/react-navigation/react-navigation/issues/9772)) ([3fb2140](https://github.com/react-navigation/react-navigation/commit/3fb21409d6d0b66266c6d5eded2014ef2ebbda0a))

# [6.1.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.9...@react-navigation/bottom-tabs@6.1.0) (2022-01-29)

### Features

* **native-stack:** export NativeStackView to support custom routers on native-stack ([#10260](https://github.com/react-navigation/react-navigation/issues/10260)) ([7b761f1](https://github.com/react-navigation/react-navigation/commit/7b761f1cc069ca68b96b5155be726024a345346f))

## [6.0.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.8...@react-navigation/bottom-tabs@6.0.9) (2021-10-12)

### Bug Fixes

* fix type errors in generated declaration file of bottom-tabs ([#10033](https://github.com/react-navigation/react-navigation/issues/10033)) ([0b40dd7](https://github.com/react-navigation/react-navigation/commit/0b40dd745b0734a4329197199e7df5c6a97cd73e)), closes [#10032](https://github.com/react-navigation/react-navigation/issues/10032)
* move [@ts-expect-error](https://github.com/ts-expect-error) to body to avoid issue in type definitions ([0a08688](https://github.com/react-navigation/react-navigation/commit/0a0868862c9d6ae77055c66938a764306d391b44))

## [6.0.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.7...@react-navigation/bottom-tabs@6.0.8) (2021-10-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [6.0.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.6...@react-navigation/bottom-tabs@6.0.7) (2021-09-26)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [6.0.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.5...@react-navigation/bottom-tabs@6.0.6) (2021-09-26)

### Bug Fixes

* export header props for other navigators ([8475481](https://github.com/react-navigation/react-navigation/commit/84754812effd8bee576c5d9836c317889dabe11a)), closes [#9965](https://github.com/react-navigation/react-navigation/issues/9965)
* stop  animations on unmount/cleanup ([5fb5f41](https://github.com/react-navigation/react-navigation/commit/5fb5f41eb6cf86ebe2f7777d6c98bda16ce71b5b))

## [6.0.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.4...@react-navigation/bottom-tabs@6.0.5) (2021-08-17)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [6.0.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.3...@react-navigation/bottom-tabs@6.0.4) (2021-08-11)

### Bug Fixes

* fix headerTransparent not working outside stack navigator ([42c43ff](https://github.com/react-navigation/react-navigation/commit/42c43ff7617112afd223ecb323be622666c79096))

## [6.0.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.2...@react-navigation/bottom-tabs@6.0.3) (2021-08-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [6.0.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.1...@react-navigation/bottom-tabs@6.0.2) (2021-08-07)

### Bug Fixes

* avoid blink when switching tab screens ([40dcbcf](https://github.com/react-navigation/react-navigation/commit/40dcbcf2fa48d5367d3121ef9f0ad6c1dd5933c6))

## [6.0.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0...@react-navigation/bottom-tabs@6.0.1) (2021-08-03)

### Bug Fixes

* preserve params when switching tabs. fixes [#9782](https://github.com/react-navigation/react-navigation/issues/9782) ([98fa233](https://github.com/react-navigation/react-navigation/commit/98fa2330146457045c01af820c6d8e8cb955f9d1))

# [6.0.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.22...@react-navigation/bottom-tabs@6.0.0) (2021-08-01)

### Bug Fixes

* remove tabBarAdapative option ([5f4e124](https://github.com/react-navigation/react-navigation/commit/5f4e12403265d22a2e4d03f11a11746b01184116))

# [6.0.0-next.22](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.21...@react-navigation/bottom-tabs@6.0.0-next.22) (2021-07-16)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.21](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.20...@react-navigation/bottom-tabs@6.0.0-next.21) (2021-07-16)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.20](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.18...@react-navigation/bottom-tabs@6.0.0-next.20) (2021-07-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.19](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.18...@react-navigation/bottom-tabs@6.0.0-next.19) (2021-06-10)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.17...@react-navigation/bottom-tabs@6.0.0-next.18) (2021-06-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.16...@react-navigation/bottom-tabs@6.0.0-next.17) (2021-05-29)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.15...@react-navigation/bottom-tabs@6.0.0-next.16) (2021-05-29)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.14...@react-navigation/bottom-tabs@6.0.0-next.15) (2021-05-27)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.13...@react-navigation/bottom-tabs@6.0.0-next.14) (2021-05-26)

### Features

* add screenListeners prop on navigators similar to screenOptions ([cde44a5](https://github.com/react-navigation/react-navigation/commit/cde44a5785444a121aa08f94af9f8fe4fc89910a))

# [6.0.0-next.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.12...@react-navigation/bottom-tabs@6.0.0-next.13) (2021-05-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.11...@react-navigation/bottom-tabs@6.0.0-next.12) (2021-05-23)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.10...@react-navigation/bottom-tabs@6.0.0-next.11) (2021-05-16)

### Bug Fixes

* fix tab bar height including extra bottom inset ([7c722d2](https://github.com/react-navigation/react-navigation/commit/7c722d2028e914e8f143b9385ebf5e1c00131a01))

### Features

* add a tabBarBackground option to bottom tabs ([2f282f1](https://github.com/react-navigation/react-navigation/commit/2f282f107053a65b69f80edb5d9c858cfa569aa2))

# [6.0.0-next.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.9...@react-navigation/bottom-tabs@6.0.0-next.10) (2021-05-10)

### Bug Fixes

* add a deprecation warning for mode prop in stack ([a6e4981](https://github.com/react-navigation/react-navigation/commit/a6e498170f59648190fa5513e273ca523e56c5d5))

### Features

* return a NavigationContent component from useNavigationBuilder ([1179d56](https://github.com/react-navigation/react-navigation/commit/1179d56c5008270753feef41acdc1dbd2191efcf))

# [6.0.0-next.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.8...@react-navigation/bottom-tabs@6.0.0-next.9) (2021-05-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.7...@react-navigation/bottom-tabs@6.0.0-next.8) (2021-05-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.6...@react-navigation/bottom-tabs@6.0.0-next.7) (2021-05-09)

### Bug Fixes

* enable screens only on supported platforms ([#9494](https://github.com/react-navigation/react-navigation/issues/9494)) ([8da4c58](https://github.com/react-navigation/react-navigation/commit/8da4c58065607d44e9dc1ad8943e09537598dcd7))
* make sure disabling react-native-screens works ([a369ba3](https://github.com/react-navigation/react-navigation/commit/a369ba36451ddc2bb5b247e61b725bce1e3fb5e5))

# [6.0.0-next.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.5...@react-navigation/bottom-tabs@6.0.0-next.6) (2021-05-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.4...@react-navigation/bottom-tabs@6.0.0-next.5) (2021-04-16)

### Bug Fixes

* update tab bar height correctly. fixes [#9296](https://github.com/react-navigation/react-navigation/issues/9296) ([338ed6f](https://github.com/react-navigation/react-navigation/commit/338ed6ff07bd2d6efa1abdb369612ea72f540502))

# [6.0.0-next.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.3...@react-navigation/bottom-tabs@6.0.0-next.4) (2021-04-08)

### Bug Fixes

* remove calls to removed Keyboard.removeListener in useIsKeyboardShown ([#9457](https://github.com/react-navigation/react-navigation/issues/9457)) ([d87857e](https://github.com/react-navigation/react-navigation/commit/d87857e5d93c19ebee2fd84eb4910e36001ec2a3))

# [6.0.0-next.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.2...@react-navigation/bottom-tabs@6.0.0-next.3) (2021-03-22)

### Bug Fixes

* use tab role on Android for accessibility ([de805a3](https://github.com/react-navigation/react-navigation/commit/de805a3ebf35db81cb7b7bcbf5cfd4a03e69c567))

### Features

* add a Background component ([cbaabc1](https://github.com/react-navigation/react-navigation/commit/cbaabc1288e780698e499a00b9ca06ab9746a0da))

# [6.0.0-next.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0-next.1...@react-navigation/bottom-tabs@6.0.0-next.2) (2021-03-12)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [6.0.0-next.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@6.0.0...@react-navigation/bottom-tabs@6.0.0-next.1) (2021-03-10)

### Bug Fixes

* fix peer dep versions ([72f90b5](https://github.com/react-navigation/react-navigation/commit/72f90b50d27eda1315bb750beca8a36f26dafe17))

# [6.0.0-next.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.11.1...@react-navigation/bottom-tabs@6.0.0-next.0) (2021-03-09)

### Bug Fixes

* add missing helper types in descriptors ([21a1154](https://github.com/react-navigation/react-navigation/commit/21a11543bf41c4559c2570d5accc0bbb3b67eb8d))
* drop usage of Dimensions in favor of metrics from safe-area-context ([12b893d](https://github.com/react-navigation/react-navigation/commit/12b893d7ca8cdb726b973972797658ac9c7d17d7))
* enable detachInactiveScreens by default on web for better a11y ([4954d6a](https://github.com/react-navigation/react-navigation/commit/4954d6aae3cdbb5855d44ff17d80d16b81fb224e))
* fix drawer and bottom tabs not being visible on web. closes [#9225](https://github.com/react-navigation/react-navigation/issues/9225) ([b735de1](https://github.com/react-navigation/react-navigation/commit/b735de153ca650240625dba6d8b5c8d16b913bac))
* fix drawer screen content not being interactable on Android ([865d8b3](https://github.com/react-navigation/react-navigation/commit/865d8b3e51e117a01243966c160b7cd147d236ac))
* fix initial metrics on server ([69d333f](https://github.com/react-navigation/react-navigation/commit/69d333f6c23e0c37eaf4d3f8b413e8f96d6827f8))
* fix pointerEvents in ResourceSavingScene ([af53dd6](https://github.com/react-navigation/react-navigation/commit/af53dd6548630124f831446e0eee468da5d9bf5e)), closes [#9241](https://github.com/react-navigation/react-navigation/issues/9241) [#9242](https://github.com/react-navigation/react-navigation/issues/9242)
* show a missing icon symbol instead of empty area in bottom tab bar ([2bc4882](https://github.com/react-navigation/react-navigation/commit/2bc4882692be9f02d781639892e1f98b891811c4))

### Code Refactoring

* don't use deprecated APIs from react-native-safe-area-context ([ddf27bf](https://github.com/react-navigation/react-navigation/commit/ddf27bf41a2efc5d1573aad0f8fe6c27a98c32b3))
* drop support for tabBarVisible option ([a97a43a](https://github.com/react-navigation/react-navigation/commit/a97a43aa1d2a615074ade93f1addebcee1dbfb65))
* move `tabBarOptions` to `options` for bottom tabs ([f7ff1ad](https://github.com/react-navigation/react-navigation/commit/f7ff1adee7654b2d624dee4ae8844be217f23026))

### Features

* initial implementation of @react-navigation/elements ([07ba7a9](https://github.com/react-navigation/react-navigation/commit/07ba7a96870efdb8acf99eb82ba0b1d3eac90bab))
* move lazy to options for bottom-tabs and drawer ([068a9a4](https://github.com/react-navigation/react-navigation/commit/068a9a456c31a08104097f2a8434c66c30a5be99))

### BREAKING CHANGES

* The lazy prop now can be configured per screen instead of for the whole navigator. To keep previous behavior, you can specify it in screenOptions
* This commit moves options from `tabBarOptions` to regular `options` in order to reduce confusion between the two, as well as to make it more flexible to configure the tab bar based on a per screen basis.
* We now require newer versions of safe area context library.
* We need to add support for specifying style for tab bar in options to support the use cases which need this.

## [5.11.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.11.0...@react-navigation/bottom-tabs@5.11.1) (2020-11-10)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.11.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.7...@react-navigation/bottom-tabs@5.11.0) (2020-11-09)

### Features

* add a hook to get bottom tab bar height ([e08c91f](https://github.com/react-navigation/react-navigation/commit/e08c91ff0a3df13dc6e6096a3e95f60722e6946b)), closes [#8037](https://github.com/react-navigation/react-navigation/issues/8037) [#8536](https://github.com/react-navigation/react-navigation/issues/8536)
* add a tabBarBadgeStyle option to customize the badge ([6ac4d40](https://github.com/react-navigation/react-navigation/commit/6ac4d40140189a29d857c4d1203bced6929f7baf))

## [5.10.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.6...@react-navigation/bottom-tabs@5.10.7) (2020-11-08)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.10.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.5...@react-navigation/bottom-tabs@5.10.6) (2020-11-04)

### Bug Fixes

* disable react-native-screens on iOS for older versions ([ce7d20e](https://github.com/react-navigation/react-navigation/commit/ce7d20e3366415b07a537e01ee0b17ce7e72cad6))

## [5.10.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.4...@react-navigation/bottom-tabs@5.10.5) (2020-11-04)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.10.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.3...@react-navigation/bottom-tabs@5.10.4) (2020-11-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.10.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.2...@react-navigation/bottom-tabs@5.10.3) (2020-11-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.10.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.1...@react-navigation/bottom-tabs@5.10.2) (2020-10-30)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.10.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.10.0...@react-navigation/bottom-tabs@5.10.1) (2020-10-28)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.10.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.9.2...@react-navigation/bottom-tabs@5.10.0) (2020-10-24)

### Features

* add optional screens per navigator ([#8805](https://github.com/react-navigation/react-navigation/issues/8805)) ([7196889](https://github.com/react-navigation/react-navigation/commit/7196889bf1218eb6a736d9475e33a909c2248c3b))
* add sceneContainerStyle prop to bottom-tabs navigator ([#8947](https://github.com/react-navigation/react-navigation/issues/8947)) ([f01bb48](https://github.com/react-navigation/react-navigation/commit/f01bb4834b01e13ab9a6b220328349f77ca49428))
* improve types for navigation state ([#8980](https://github.com/react-navigation/react-navigation/issues/8980)) ([7dc2f58](https://github.com/react-navigation/react-navigation/commit/7dc2f5832e371473f3263c01ab39824eb9e2057d))
* update helper types to have navigator specific methods ([f51086e](https://github.com/react-navigation/react-navigation/commit/f51086edea42f2382dac8c6914aac8574132114b))

## [5.9.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.9.1...@react-navigation/bottom-tabs@5.9.2) (2020-10-07)

### Bug Fixes

* use route keys instead of index for lazy load ([c49dab3](https://github.com/react-navigation/react-navigation/commit/c49dab31b2c63a1735f0ed0a1936ecf7bbcd8b13))

## [5.9.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.9.0...@react-navigation/bottom-tabs@5.9.1) (2020-09-28)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.9.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.8.0...@react-navigation/bottom-tabs@5.9.0) (2020-09-22)

### Bug Fixes

* cleanly removing event listeners in useWindowDimensions ([#8866](https://github.com/react-navigation/react-navigation/issues/8866)) ([dcbfe52](https://github.com/react-navigation/react-navigation/commit/dcbfe52667d14b0dbed6a353675d02189f7f7b5b))
* fix border showing on hidden tab bar. closes [#8869](https://github.com/react-navigation/react-navigation/issues/8869) ([9b03c8e](https://github.com/react-navigation/react-navigation/commit/9b03c8e3a4e1552f9abaa2732a64e2136821e38d))

### Features

* add compact size for bottom tabs ([#8728](https://github.com/react-navigation/react-navigation/issues/8728)) ([a6179b7](https://github.com/react-navigation/react-navigation/commit/a6179b75adba2282a9c12751bb8db3751c39d8e4))

# [5.8.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.7.3...@react-navigation/bottom-tabs@5.8.0) (2020-08-04)

### Bug Fixes

* only offset bottom tab on iOS. fixes [#8642](https://github.com/react-navigation/react-navigation/issues/8642) ([d979dfd](https://github.com/react-navigation/react-navigation/commit/d979dfd634c3969342803f7d988ff3439b4fe9e5))

### Features

* allow full configuration of tab bar hide animation ([0b62730](https://github.com/react-navigation/react-navigation/commit/0b627304aa9fbb48de7a674ff8dfbbc495f5efe6))
* expose `BottomTabBarButtonProps` type ([#8649](https://github.com/react-navigation/react-navigation/issues/8649)) ([a35ac81](https://github.com/react-navigation/react-navigation/commit/a35ac813b6b0816cef93b54792f2164f9b82d55e))
* user can specify how long tab hide animation should take ([#8587](https://github.com/react-navigation/react-navigation/issues/8587)) ([b0cafb3](https://github.com/react-navigation/react-navigation/commit/b0cafb3c4928df32bb9f866649ad0fbf1ad53d95))

## [5.7.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.7.2...@react-navigation/bottom-tabs@5.7.3) (2020-07-28)

### Bug Fixes

* add accessibilityState property ([#8548](https://github.com/react-navigation/react-navigation/issues/8548)) ([ce4eb7e](https://github.com/react-navigation/react-navigation/commit/ce4eb7e9273a25e4433eb82e255a58ba3bf4d632))
* pass label position flag to label rendering in BottomTabBar ([#8557](https://github.com/react-navigation/react-navigation/issues/8557)) ([baea77e](https://github.com/react-navigation/react-navigation/commit/baea77e3325f0d7e5ce331ad61979a9362dd01fa))

## [5.7.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.7.1...@react-navigation/bottom-tabs@5.7.2) (2020-07-19)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.7.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.7.0...@react-navigation/bottom-tabs@5.7.1) (2020-07-14)

### Bug Fixes

* don't render badge on bottom tabs if not visible. closes [#8577](https://github.com/react-navigation/react-navigation/issues/8577) ([2f74541](https://github.com/react-navigation/react-navigation/commit/2f74541811bac4d36e89c159cd1f4b267063e7f9))

# [5.7.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.6.1...@react-navigation/bottom-tabs@5.7.0) (2020-07-10)

### Bug Fixes

* fix bottom tab bar to match iOS defaults ([849e04a](https://github.com/react-navigation/react-navigation/commit/849e04ab6a541fffb490ffdfa9819608b88494f4))

### Features

* add support for badges to bottom tab bar ([96c7b68](https://github.com/react-navigation/react-navigation/commit/96c7b688ce773b3dd1f1cf7775367cd7080c94a2))

## [5.6.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.6.0...@react-navigation/bottom-tabs@5.6.1) (2020-06-25)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.6.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/bottom-tabs@5.5.2...@react-navigation/bottom-tabs@5.6.0) (2020-06-24)

### Bug Fixes

* make sure we don't miss dimensions updates ([c65f9ef](https://github.com/react-navigation/react-navigation/commit/c65f9ef1a9be93b399e724a9731605e408aca80e))
* remove broken showIcon option from bottom-tabs ([1612819](https://github.com/react-navigation/react-navigation/commit/16128199edd4de37f9c7353bdf803de8e2f201a2))

### Features

* add iconStyle prop to bottom tab bar options ([#8188](https://github.com/react-navigation/react-navigation/issues/8188)) ([4480d2f](https://github.com/react-navigation/react-navigation/commit/4480d2fe04c8da11b444ebe75ee618d380682312))

## [5.5.2](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.5.1...@react-navigation/bottom-tabs@5.5.2) (2020-06-06)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.5.1](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.5.0...@react-navigation/bottom-tabs@5.5.1) (2020-05-27)

### Bug Fixes

* fix type of style for various options ([9d822b9](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/9d822b95a6df797e2e63e481573e64ea7d0f9386))

# [5.5.0](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.7...@react-navigation/bottom-tabs@5.5.0) (2020-05-23)

### Features

* animate changes to tabBarVisible in BottomTabBar ([#8286](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/8286)) ([c1e46f8](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/c1e46f8e331e0054995aa476455af204d02d4170))
* update react-native-safe-area-context to 1.0.0 ([#8182](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/8182)) ([d62fbfe](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/d62fbfe255140f16b182e8b54b276a7c96f2aec6))

## [5.4.7](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.6...@react-navigation/bottom-tabs@5.4.7) (2020-05-20)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.4.6](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.5...@react-navigation/bottom-tabs@5.4.6) (2020-05-20)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.4.5](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.4...@react-navigation/bottom-tabs@5.4.5) (2020-05-16)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.4.4](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.3...@react-navigation/bottom-tabs@5.4.4) (2020-05-14)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.4.3](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.2...@react-navigation/bottom-tabs@5.4.3) (2020-05-14)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.4.2](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.1...@react-navigation/bottom-tabs@5.4.2) (2020-05-10)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.4.1](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.4.0...@react-navigation/bottom-tabs@5.4.1) (2020-05-08)

### Bug Fixes

* fix building typescript definitions. closes [#8216](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/8216) ([47a1229](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/47a12298378747edd2d22e54dc1c8677f98c49b4))

# [5.4.0](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.3.4...@react-navigation/bottom-tabs@5.4.0) (2020-05-08)

### Features

* add generic type aliases for screen props ([bea14aa](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/bea14aa26fd5cbfebc7973733c5cf1f44fd323aa)), closes [#7971](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/7971)

## [5.3.4](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.3.3...@react-navigation/bottom-tabs@5.3.4) (2020-05-05)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.3.3](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.3.2...@react-navigation/bottom-tabs@5.3.3) (2020-05-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.3.2](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.3.1...@react-navigation/bottom-tabs@5.3.2) (2020-05-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.3.1](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.3.0...@react-navigation/bottom-tabs@5.3.1) (2020-04-30)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.3.0](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.8...@react-navigation/bottom-tabs@5.3.0) (2020-04-30)

### Features

* add `useLinkBuilder` hook to build links ([2792f43](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/2792f438fe45428fe193e3708fee7ad61966cbf4))
* add action prop to Link ([942d2be](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/942d2be2c72720469475ce12ec8df23825994dbf))

## [5.2.8](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.7...@react-navigation/bottom-tabs@5.2.8) (2020-04-27)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.2.7](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.6...@react-navigation/bottom-tabs@5.2.7) (2020-04-17)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.2.6](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.5...@react-navigation/bottom-tabs@5.2.6) (2020-04-08)

### Bug Fixes

* mark type exports for all packages ([b71de6c](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/b71de6cc799143f1d0e8a0cfcc34f0a2381f9840))

## [5.2.5](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.4...@react-navigation/bottom-tabs@5.2.5) (2020-03-30)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.2.4](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.3...@react-navigation/bottom-tabs@5.2.4) (2020-03-23)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.2.3](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.2...@react-navigation/bottom-tabs@5.2.3) (2020-03-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.2.2](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.1...@react-navigation/bottom-tabs@5.2.2) (2020-03-19)

### Bug Fixes

* don't use react-native-screens on web ([b1a65fc](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/b1a65fc73e8603ae2c06ef101a74df31e80bb9b2)), closes [#7485](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/7485)
* initialize height and width to zero if undefined ([3df65e2](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/3df65e28197db3bb8371059146546d57661c5ba3)), closes [#6789](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/6789)

## [5.2.1](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.2.0...@react-navigation/bottom-tabs@5.2.1) (2020-03-17)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.2.0](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.1.1...@react-navigation/bottom-tabs@5.2.0) (2020-03-16)

### Features

* add safeAreaInsets to bottom tabs ([82af7be](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/82af7bed7135e42e24693b48cf7f1c6f9f5a6981))

## [5.1.1](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.1.0...@react-navigation/bottom-tabs@5.1.1) (2020-03-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.1.0](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.7...@react-navigation/bottom-tabs@5.1.0) (2020-02-26)

### Features

* add ability add listeners with listeners prop ([1624108](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/162410843c4f175ae107756de1c3af04d1d47aa7)), closes [#6756](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/6756)

## [5.0.7](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.6...@react-navigation/bottom-tabs@5.0.7) (2020-02-21)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.0.6](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.5...@react-navigation/bottom-tabs@5.0.6) (2020-02-19)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.0.5](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.4...@react-navigation/bottom-tabs@5.0.5) (2020-02-14)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.0.4](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.3...@react-navigation/bottom-tabs@5.0.4) (2020-02-14)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.0.3](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.2...@react-navigation/bottom-tabs@5.0.3) (2020-02-12)

**Note:** Version bump only for package @react-navigation/bottom-tabs

## [5.0.2](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.1...@react-navigation/bottom-tabs@5.0.2) (2020-02-11)

### Bug Fixes

* initialize keyboard-hiding tabBar to visible=true ([#6740](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/6740), [#6799](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/6799)) ([0c59ef7](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/0c59ef7328c63108a2a2c04e927794d73cead63a))
* provide route context to header and bottom tabs ([b6e7e08](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/b6e7e08b9a05be6c04ed21e938b9580876239116))

## [5.0.1](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.45...@react-navigation/bottom-tabs@5.0.1) (2020-02-10)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.45](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.44...@react-navigation/bottom-tabs@5.0.0-alpha.45) (2020-02-04)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.44](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.43...@react-navigation/bottom-tabs@5.0.0-alpha.44) (2020-02-04)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.43](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.42...@react-navigation/bottom-tabs@5.0.0-alpha.43) (2020-02-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.42](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.39...@react-navigation/bottom-tabs@5.0.0-alpha.42) (2020-02-02)

### Bug Fixes

* add licenses ([0c159db](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/0c159db4c9bc85e83b5cfe6819ab2562669a4d8f))

# [5.0.0-alpha.40](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.39...@react-navigation/bottom-tabs@5.0.0-alpha.40) (2020-02-02)

### Bug Fixes

* add licenses ([0c159db](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/0c159db4c9bc85e83b5cfe6819ab2562669a4d8f))

# [5.0.0-alpha.39](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.38...@react-navigation/bottom-tabs@5.0.0-alpha.39) (2020-01-24)

### Bug Fixes

* use layout instead of dimensions for determining tab bar layout ([f1fe951](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/f1fe951cf9d602e1b6d4932e3c6c77bbeaaec5c0))

# [5.0.0-alpha.38](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.37...@react-navigation/bottom-tabs@5.0.0-alpha.38) (2020-01-23)

### Bug Fixes

* don't use native driver on web ([0a982ee](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/0a982ee6984b24c0ba053a30223e255f3835e050))

### Features

* let the navigator specify if default can be prevented ([da67e13](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/da67e134d2157201360427d3c10da24f24cae7aa))

# [5.0.0-alpha.37](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.36...@react-navigation/bottom-tabs@5.0.0-alpha.37) (2020-01-14)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.36](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.35...@react-navigation/bottom-tabs@5.0.0-alpha.36) (2020-01-13)

### Bug Fixes

* make sure paths aren't aliased when building definitions ([65a5dac](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/commit/65a5dac2bf887f4ba081ab15bd4c9870bb15697f)), closes [#265](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/issues/265)

# [5.0.0-alpha.35](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.34...@react-navigation/bottom-tabs@5.0.0-alpha.35) (2020-01-13)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.34](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.32...@react-navigation/bottom-tabs@5.0.0-alpha.34) (2020-01-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.33](https://github.com/react-navigation/react-navigation/tree/main/packages/bottom-tabs/compare/@react-navigation/bottom-tabs@5.0.0-alpha.32...@react-navigation/bottom-tabs@5.0.0-alpha.33) (2020-01-09)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.32](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.31...@react-navigation/bottom-tabs@5.0.0-alpha.32) (2020-01-05)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.31](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.30...@react-navigation/bottom-tabs@5.0.0-alpha.31) (2020-01-03)

### Bug Fixes

* provide initial values for safe area to prevent blank screen ([#238](https://github.com/react-navigation/navigation-ex/issues/238)) ([77b7570](https://github.com/react-navigation/navigation-ex/commit/77b757091c0451e20bca01138629669c7da544a8))

# [5.0.0-alpha.30](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.29...@react-navigation/bottom-tabs@5.0.0-alpha.30) (2020-01-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.29](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.28...@react-navigation/bottom-tabs@5.0.0-alpha.29) (2020-01-01)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.28](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.27...@react-navigation/bottom-tabs@5.0.0-alpha.28) (2019-12-19)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.27](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.26...@react-navigation/bottom-tabs@5.0.0-alpha.27) (2019-12-16)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.26](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.25...@react-navigation/bottom-tabs@5.0.0-alpha.26) (2019-12-14)

### Features

* add custom theme support ([#211](https://github.com/react-navigation/navigation-ex/issues/211)) ([00fc616](https://github.com/react-navigation/navigation-ex/commit/00fc616de0572bade8aa85052cdc8290360b1d7f))

# [5.0.0-alpha.25](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.24...@react-navigation/bottom-tabs@5.0.0-alpha.25) (2019-12-11)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.24](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.23...@react-navigation/bottom-tabs@5.0.0-alpha.24) (2019-12-10)

### Bug Fixes

* fix accessibility label in bottom tab bar ([448fa64](https://github.com/react-navigation/navigation-ex/commit/448fa642edcc282cd18ac034fd5baa6529a6c0f8))

# [5.0.0-alpha.23](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.22...@react-navigation/bottom-tabs@5.0.0-alpha.23) (2019-12-07)

### Features

* export underlying views used to build navigators ([#191](https://github.com/react-navigation/navigation-ex/issues/191)) ([d618ab3](https://github.com/react-navigation/navigation-ex/commit/d618ab382ecc5eccbcd5faa89e76f9ed2d75f405))

# [5.0.0-alpha.22](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.21...@react-navigation/bottom-tabs@5.0.0-alpha.22) (2019-11-17)

### Bug Fixes

* workaround SafereaProvider causing jumping ([c17ad18](https://github.com/react-navigation/navigation-ex/commit/c17ad18b20cb05c577e1235a58ccc1c856fee086)), closes [#174](https://github.com/react-navigation/navigation-ex/issues/174)

# [5.0.0-alpha.21](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.20...@react-navigation/bottom-tabs@5.0.0-alpha.21) (2019-11-10)

### Bug Fixes

* make bottom tab bar consistent across platforms ([d1ca7f9](https://github.com/react-navigation/navigation-ex/commit/d1ca7f9))

# [5.0.0-alpha.20](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.19...@react-navigation/bottom-tabs@5.0.0-alpha.20) (2019-11-08)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.19](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.18...@react-navigation/bottom-tabs@5.0.0-alpha.19) (2019-11-04)

### Bug Fixes

* fix default BottomTabBar button ([#161](https://github.com/react-navigation/navigation-ex/issues/161)) ([22cb675](https://github.com/react-navigation/navigation-ex/commit/22cb675))

# [5.0.0-alpha.18](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.17...@react-navigation/bottom-tabs@5.0.0-alpha.18) (2019-11-04)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.17](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.16...@react-navigation/bottom-tabs@5.0.0-alpha.17) (2019-10-30)

### Bug Fixes

* hide inactive pages from screen reader in tabs ([#148](https://github.com/react-navigation/navigation-ex/issues/148)) ([58f7115](https://github.com/react-navigation/navigation-ex/commit/58f7115))

### Features

* add an 'unmountInactiveScreens' option ([12d597f](https://github.com/react-navigation/navigation-ex/commit/12d597f))

# [5.0.0-alpha.16](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.15...@react-navigation/bottom-tabs@5.0.0-alpha.16) (2019-10-29)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.15](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.14...@react-navigation/bottom-tabs@5.0.0-alpha.15) (2019-10-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.14](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.13...@react-navigation/bottom-tabs@5.0.0-alpha.14) (2019-10-15)

### Bug Fixes

* improve keyboard handling with bottom tab bar ([42beb66](https://github.com/react-navigation/navigation-ex/commit/42beb66))
* make it possible to run the example on web ([7a901af](https://github.com/react-navigation/navigation-ex/commit/7a901af))

### Features

* initial version of native stack ([#102](https://github.com/react-navigation/navigation-ex/issues/102)) ([ba3f718](https://github.com/react-navigation/navigation-ex/commit/ba3f718))

# [5.0.0-alpha.13](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.12...@react-navigation/bottom-tabs@5.0.0-alpha.13) (2019-10-06)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.12](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.11...@react-navigation/bottom-tabs@5.0.0-alpha.12) (2019-10-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.11](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.10...@react-navigation/bottom-tabs@5.0.0-alpha.11) (2019-10-03)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.10](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.9...@react-navigation/bottom-tabs@5.0.0-alpha.10) (2019-09-27)

### Features

* export BottomTabBar props type ([#109](https://github.com/react-navigation/navigation-ex/issues/109)) ([1fd5a86](https://github.com/react-navigation/navigation-ex/commit/1fd5a86))
* export some more type aliases ([8b78d61](https://github.com/react-navigation/navigation-ex/commit/8b78d61))

# [5.0.0-alpha.9](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.8...@react-navigation/bottom-tabs@5.0.0-alpha.9) (2019-09-17)

### Bug Fixes

* provide navigation prop in header ([30e510d](https://github.com/react-navigation/navigation-ex/commit/30e510d))

# [5.0.0-alpha.8](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.7...@react-navigation/bottom-tabs@5.0.0-alpha.8) (2019-09-16)

### Features

* make example run as bare react-native project as well ([#85](https://github.com/react-navigation/navigation-ex/issues/85)) ([d16c20c](https://github.com/react-navigation/navigation-ex/commit/d16c20c))

# [5.0.0-alpha.7](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.6...@react-navigation/bottom-tabs@5.0.0-alpha.7) (2019-08-31)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.6](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.5...@react-navigation/bottom-tabs@5.0.0-alpha.6) (2019-08-29)

### Bug Fixes

* allow making params optional. fixes [#80](https://github.com/react-navigation/navigation-ex/issues/80) ([a9d4813](https://github.com/react-navigation/navigation-ex/commit/a9d4813))
* types path ([#75](https://github.com/react-navigation/navigation-ex/issues/75)) ([b4a5c3c](https://github.com/react-navigation/navigation-ex/commit/b4a5c3c))

# [5.0.0-alpha.5](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.4...@react-navigation/bottom-tabs@5.0.0-alpha.5) (2019-08-28)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# [5.0.0-alpha.4](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.3...@react-navigation/bottom-tabs@5.0.0-alpha.4) (2019-08-27)

### Features

* add hook to scroll to top on tab press ([9e1104c](https://github.com/react-navigation/navigation-ex/commit/9e1104c))

# [5.0.0-alpha.3](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.2...@react-navigation/bottom-tabs@5.0.0-alpha.3) (2019-08-22)

### Bug Fixes

* fix path to typescript definitions ([f182315](https://github.com/react-navigation/navigation-ex/commit/f182315))

# [5.0.0-alpha.2](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/bottom-tabs@5.0.0-alpha.1...@react-navigation/bottom-tabs@5.0.0-alpha.2) (2019-08-22)

**Note:** Version bump only for package @react-navigation/bottom-tabs

# 5.0.0-alpha.1 (2019-08-21)

### Bug Fixes

* change opacity for hidden tabs only when not using rn-screens ([#80](https://github.com/react-navigation/navigation-ex/issues/80)) ([6490049](https://github.com/react-navigation/navigation-ex/commit/6490049)), closes [#5382](https://github.com/react-navigation/navigation-ex/issues/5382)
* correctly reset _isTabPress property ([80b7f1d](https://github.com/react-navigation/navigation-ex/commit/80b7f1d))
* fix hit slop for bottom tab bar ([#110](https://github.com/react-navigation/navigation-ex/issues/110)) ([ce3670b](https://github.com/react-navigation/navigation-ex/commit/ce3670b))
* fix peer deps and add git urls ([6b4fc74](https://github.com/react-navigation/navigation-ex/commit/6b4fc74))
* fix tabBarOnPress with MaterialTopTabs and fix isFocused ([#21](https://github.com/react-navigation/navigation-ex/issues/21)) ([491ee81](https://github.com/react-navigation/navigation-ex/commit/491ee81))
* import SceneView from react-navigation default export ([5d5f4d1](https://github.com/react-navigation/navigation-ex/commit/5d5f4d1))
* increase padding for iOS horizontal label alignment ([#114](https://github.com/react-navigation/navigation-ex/issues/114)) ([4adb3a9](https://github.com/react-navigation/navigation-ex/commit/4adb3a9)), closes [#113](https://github.com/react-navigation/navigation-ex/issues/113)
* iPad / horizontal layout works as expected in BottomTabBar ([3bb5ec4](https://github.com/react-navigation/navigation-ex/commit/3bb5ec4))
* NavigationActions.popToTop no longer exists, belongs to StackActions now ([273131f](https://github.com/react-navigation/navigation-ex/commit/273131f))
* remove tab icon wrapper to fix adaptive icons ([3fdb3d9](https://github.com/react-navigation/navigation-ex/commit/3fdb3d9))
* specify default values for getAccessibilityX ([3c7918d](https://github.com/react-navigation/navigation-ex/commit/3c7918d)), closes [#116](https://github.com/react-navigation/navigation-ex/issues/116)
* tweak hitSlop on bottom tab bar buttons ([a5514a2](https://github.com/react-navigation/navigation-ex/commit/a5514a2))
* typo in accessibilityLabel ([57a0d46](https://github.com/react-navigation/navigation-ex/commit/57a0d46))
* use react-lifecycles-compat for async mode compatibility ([93b45f2](https://github.com/react-navigation/navigation-ex/commit/93b45f2))
* use the JUMP_TO action for tab change ([242625a](https://github.com/react-navigation/navigation-ex/commit/242625a))

### Features

* add ability to render label beside the icon ([#103](https://github.com/react-navigation/navigation-ex/issues/103)) ([8f70ebb](https://github.com/react-navigation/navigation-ex/commit/8f70ebb))
* add accessibility role and state to bottom bar ([#90](https://github.com/react-navigation/navigation-ex/issues/90)) ([73e9b4c](https://github.com/react-navigation/navigation-ex/commit/73e9b4c))
* add accessibilityLabel and testID options ([#26](https://github.com/react-navigation/navigation-ex/issues/26)) ([4cc91d1](https://github.com/react-navigation/navigation-ex/commit/4cc91d1))
* add an option to swap out TouchableWithoutFeedback for another component ([#27](https://github.com/react-navigation/navigation-ex/issues/27)) ([34b0e5d](https://github.com/react-navigation/navigation-ex/commit/34b0e5d))
* add defaultHandler argument to tabBarOnPress. fixes [#22](https://github.com/react-navigation/navigation-ex/issues/22) ([267e9ec](https://github.com/react-navigation/navigation-ex/commit/267e9ec))
* add lazy option. fixes [#23](https://github.com/react-navigation/navigation-ex/issues/23) ([2a80c11](https://github.com/react-navigation/navigation-ex/commit/2a80c11))
* export individual navigators separately. fixes [#2](https://github.com/react-navigation/navigation-ex/issues/2) ([65b0c46](https://github.com/react-navigation/navigation-ex/commit/65b0c46))
* export tab bars ([a4ead48](https://github.com/react-navigation/navigation-ex/commit/a4ead48))
* hide tab bar when keyboard is shown ([#112](https://github.com/react-navigation/navigation-ex/issues/112)) ([ccb2d38](https://github.com/react-navigation/navigation-ex/commit/ccb2d38)), closes [#16](https://github.com/react-navigation/navigation-ex/issues/16)
* implement various navigators ([f0b80ce](https://github.com/react-navigation/navigation-ex/commit/f0b80ce))
* initial commit ([89934b9](https://github.com/react-navigation/navigation-ex/commit/89934b9))
* lazy initialized MaterialTopTabNavigator routes ([#9](https://github.com/react-navigation/navigation-ex/issues/9)) ([18fa131](https://github.com/react-navigation/navigation-ex/commit/18fa131))
* put material bottom tabs in another repository ([42e35f5](https://github.com/react-navigation/navigation-ex/commit/42e35f5))
* upgrade react-native-tab-view to 2.0 ([d8b4774](https://github.com/react-navigation/navigation-ex/commit/d8b4774))
* use resource saving view for scenes. fixes [#3](https://github.com/react-navigation/navigation-ex/issues/3) ([fd2c352](https://github.com/react-navigation/navigation-ex/commit/fd2c352))
