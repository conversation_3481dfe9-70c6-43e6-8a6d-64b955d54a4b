# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [7.1.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.1.1...@react-navigation/stack@7.1.2) (2025-03-02)

**Note:** Version bump only for package @react-navigation/stack

## [7.1.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.1.0...@react-navigation/stack@7.1.1) (2024-12-18)

### Bug Fixes

* fix page not being resized to fit the screen on web ([9e398ea](https://github.com/react-navigation/react-navigation/commit/9e398ea4587833c16d43f5722ea07a21d66337b1)), closes [#12351](https://github.com/react-navigation/react-navigation/issues/12351) - by @

# [7.1.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.18...@react-navigation/stack@7.1.0) (2024-12-12)

### Bug Fixes

* don't freeze active and preloaded screens ([#12332](https://github.com/react-navigation/react-navigation/issues/12332)) ([015d94d](https://github.com/react-navigation/react-navigation/commit/015d94d61e8631c6f4d5471ca3c3372fe477e930)) - by @WoLewicki

### Features

* export *NavigatorProps for each navigator ([#12327](https://github.com/react-navigation/react-navigation/issues/12327)) ([316e2ff](https://github.com/react-navigation/react-navigation/commit/316e2ff7126c2c1e38ddd7296342a07155f78817)) - by @marklawlor

## [7.0.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.17...@react-navigation/stack@7.0.18) (2024-12-02)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.16...@react-navigation/stack@7.0.17) (2024-12-01)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.15...@react-navigation/stack@7.0.16) (2024-12-01)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.14...@react-navigation/stack@7.0.15) (2024-12-01)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.13...@react-navigation/stack@7.0.14) (2024-11-28)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.12...@react-navigation/stack@7.0.13) (2024-11-27)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.11...@react-navigation/stack@7.0.12) (2024-11-26)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.10...@react-navigation/stack@7.0.11) (2024-11-25)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.9...@react-navigation/stack@7.0.10) (2024-11-25)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.8...@react-navigation/stack@7.0.9) (2024-11-25)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.7...@react-navigation/stack@7.0.8) (2024-11-25)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.6...@react-navigation/stack@7.0.7) (2024-11-25)

### Bug Fixes

* animate rearranging screens as push ([dd16a57](https://github.com/react-navigation/react-navigation/commit/dd16a5715783a3fc02f899ad2a0470f7b22b04eb)) - by @

## [7.0.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.5...@react-navigation/stack@7.0.6) (2024-11-22)

### Bug Fixes

* remove background from stack ([eaadbeb](https://github.com/react-navigation/react-navigation/commit/eaadbebb67d3d0a49d301b23b981d9e630bb9b43)), closes [#12287](https://github.com/react-navigation/react-navigation/issues/12287) - by @

## [7.0.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.4...@react-navigation/stack@7.0.5) (2024-11-19)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.3...@react-navigation/stack@7.0.4) (2024-11-18)

### Bug Fixes

* fix deprecation warning for shadow styles on react-native-web ([#12253](https://github.com/react-navigation/react-navigation/issues/12253)) ([4d444f7](https://github.com/react-navigation/react-navigation/commit/4d444f77a446b622d75e6e19a3cf1c024d248a2d)), closes [#000](https://github.com/react-navigation/react-navigation/issues/000) [#000](https://github.com/react-navigation/react-navigation/issues/000) [#000](https://github.com/react-navigation/react-navigation/issues/000) - by @kubabutkiewicz

## [7.0.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.2...@react-navigation/stack@7.0.3) (2024-11-15)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.1...@react-navigation/stack@7.0.2) (2024-11-14)

**Note:** Version bump only for package @react-navigation/stack

## [7.0.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0...@react-navigation/stack@7.0.1) (2024-11-13)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.29...@react-navigation/stack@7.0.0) (2024-11-06)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.29](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.28...@react-navigation/stack@7.0.0-rc.29) (2024-10-29)

### Bug Fixes

* bump peer dep version requirement for screens ([63f1687](https://github.com/react-navigation/react-navigation/commit/63f16871c4db0c275c2d393f668adec45d31ac7a)) - by @satya164

# [7.0.0-rc.28](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.27...@react-navigation/stack@7.0.0-rc.28) (2024-10-24)

### Bug Fixes

* fix headerBack and canGoBack parameters ([41d2ac2](https://github.com/react-navigation/react-navigation/commit/41d2ac2edd6d6b539adc8e956119b9b8e5836176)) - by @satya164
* use * for react-native peer dep to support pre-release versions ([07267e5](https://github.com/react-navigation/react-navigation/commit/07267e54be752f600f808ec2898e5d76a1bc1d43)) - by @satya164

# [7.0.0-rc.27](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.26...@react-navigation/stack@7.0.0-rc.27) (2024-10-11)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.26](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.25...@react-navigation/stack@7.0.0-rc.26) (2024-09-10)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.25](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.24...@react-navigation/stack@7.0.0-rc.25) (2024-09-08)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.24](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.23...@react-navigation/stack@7.0.0-rc.24) (2024-08-09)

### Bug Fixes

* fix alignment for center aligned title ([6e22370](https://github.com/react-navigation/react-navigation/commit/6e223705b8d8cf98b2e6e7fd8119571bffb11c61)) - by @satya164
* tweak UIKit header animation ([f6a3ef7](https://github.com/react-navigation/react-navigation/commit/f6a3ef7d7a9014b7008361b07dda73e5e1f24f01)) - by @satya164

# [7.0.0-rc.23](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.22...@react-navigation/stack@7.0.0-rc.23) (2024-08-08)

### Bug Fixes

* avoid using column-reverse for header for a11y on web ([3101d04](https://github.com/react-navigation/react-navigation/commit/3101d0406c32163c0576cb5c4712755c01b1a17c)) - by @satya164
* improve custom header in native stack & stack ([7e6b666](https://github.com/react-navigation/react-navigation/commit/7e6b6662342e63d241c1a2e8f57c56a3b5b0cef5)) - by @satya164

# [7.0.0-rc.22](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.21...@react-navigation/stack@7.0.0-rc.22) (2024-08-07)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.21](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.20...@react-navigation/stack@7.0.0-rc.21) (2024-08-05)

### Bug Fixes

* fix incorrect value from useHeaderHeight for stack ([add9a17](https://github.com/react-navigation/react-navigation/commit/add9a17f0c91191c9347e59449d304f7f85239ab)) - by @satya164

### Features

* add headerBackButtonDisplayMode for native stack ([#12089](https://github.com/react-navigation/react-navigation/issues/12089)) ([89ffa1b](https://github.com/react-navigation/react-navigation/commit/89ffa1baa1dc3ad8260361a3f84aa21d24c1643e)), closes [#11980](https://github.com/react-navigation/react-navigation/issues/11980) - by @dylancom
* add headerBackButtonDisplayMode for stack ([#12090](https://github.com/react-navigation/react-navigation/issues/12090)) ([35cd213](https://github.com/react-navigation/react-navigation/commit/35cd213d366a60afe9955cf10dffb83d9006ce73)) - by @satya164

### BREAKING CHANGES

* This removes the `headerBackTitleVisible` option, and
changes `headerTruncatedBackTitle` to `headerBackTruncatedTitle`.

Similarly, `headerLeft` now receives `displayMode` instead of
`labelVisible`, and `HeaderBackButton` accepts `displayMode` instead of
`labelVisible`
* This removes the `headerBackTitleVisible` option,

Adds corresponding functionality from
https://github.com/software-mansion/react-native-screens/pull/2123.

# [7.0.0-rc.20](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.19...@react-navigation/stack@7.0.0-rc.20) (2024-08-02)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.19](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.18...@react-navigation/stack@7.0.0-rc.19) (2024-08-01)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.17...@react-navigation/stack@7.0.0-rc.18) (2024-07-25)

### Bug Fixes

* fix type inference for params. closes [#12071](https://github.com/react-navigation/react-navigation/issues/12071) ([3299b70](https://github.com/react-navigation/react-navigation/commit/3299b70682adbf55811369535cca1cdd0dc59860)) - by @

# [7.0.0-rc.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.16...@react-navigation/stack@7.0.0-rc.17) (2024-07-19)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.15...@react-navigation/stack@7.0.0-rc.16) (2024-07-12)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.14...@react-navigation/stack@7.0.0-rc.15) (2024-07-12)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.13...@react-navigation/stack@7.0.0-rc.14) (2024-07-11)

### Bug Fixes

* upgrade react-native-builder-bob ([1575287](https://github.com/react-navigation/react-navigation/commit/1575287d40fadb97f33eb19c2914d8be3066b47a)) - by @

# [7.0.0-rc.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.12...@react-navigation/stack@7.0.0-rc.13) (2024-07-11)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.11...@react-navigation/stack@7.0.0-rc.12) (2024-07-10)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.10...@react-navigation/stack@7.0.0-rc.11) (2024-07-08)

### Bug Fixes

* use a stylesheet for workaround to avoid overwriting style ([1632dfc](https://github.com/react-navigation/react-navigation/commit/1632dfc4056737dfaf561bf026f4a3810252cb6f)) - by @satya164

# [7.0.0-rc.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.9...@react-navigation/stack@7.0.0-rc.10) (2024-07-07)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.8...@react-navigation/stack@7.0.0-rc.9) (2024-07-04)

### Bug Fixes

* fix published files ([829caa0](https://github.com/react-navigation/react-navigation/commit/829caa019e125811eea5213fd380e8e1bdbe7030)) - by @

# [7.0.0-rc.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.7...@react-navigation/stack@7.0.0-rc.8) (2024-07-04)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.6...@react-navigation/stack@7.0.0-rc.7) (2024-07-04)

### Features

* add package.json exports field ([1435cfe](https://github.com/react-navigation/react-navigation/commit/1435cfe3300767c221ebd4613479ad662d61efee)) - by @

# [7.0.0-rc.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.5...@react-navigation/stack@7.0.0-rc.6) (2024-07-02)

### Bug Fixes

* drop leftover empty string for headerBackTitleVisible ([b93f861](https://github.com/react-navigation/react-navigation/commit/b93f86155fe9185c5197cd6d44b625aabb8ca4a7)) - by @satya164

# [7.0.0-rc.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.4...@react-navigation/stack@7.0.0-rc.5) (2024-07-01)

### Bug Fixes

* fix UIKit interpolation style for header ([61c1c9a](https://github.com/react-navigation/react-navigation/commit/61c1c9a873334e803d152f2295beca58bb785c47)) - by @
* stop using react-native field in package.json ([efc33cb](https://github.com/react-navigation/react-navigation/commit/efc33cb0c4830a84ceae034dc1278c54f1faf32d)) - by @

# [7.0.0-rc.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.3...@react-navigation/stack@7.0.0-rc.4) (2024-06-29)

### Bug Fixes

* add a workaround for incorrect inference [#12041](https://github.com/react-navigation/react-navigation/issues/12041) ([85c4bbb](https://github.com/react-navigation/react-navigation/commit/85c4bbbf535cde2ba9cd537a2a5ce34f060d32b9)) - by @

# [7.0.0-rc.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.2...@react-navigation/stack@7.0.0-rc.3) (2024-06-28)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.1...@react-navigation/stack@7.0.0-rc.2) (2024-06-28)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-rc.0...@react-navigation/stack@7.0.0-rc.1) (2024-06-28)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-rc.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.20...@react-navigation/stack@7.0.0-rc.0) (2024-06-27)

### Bug Fixes

* add hover effect to buttons on iPad & VisionOS ([2cb77c0](https://github.com/react-navigation/react-navigation/commit/2cb77c0ce42575275dd723555d0ec9ae7be32c66)) - by @satya164

### Features

* add a horizontal fade animation for android 14 ([0722900](https://github.com/react-navigation/react-navigation/commit/07229006c6b28d7225f57e5150914a1426071f4e)) - by @satya164

# [7.0.0-alpha.20](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.19...@react-navigation/stack@7.0.0-alpha.20) (2024-03-25)

### Features

* pass href to headerLeft function ([ce6d885](https://github.com/react-navigation/react-navigation/commit/ce6d88559e4a1afeafa84fc839892bb846349d67)) - by @satya164

# [7.0.0-alpha.19](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.18...@react-navigation/stack@7.0.0-alpha.19) (2024-03-22)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.17...@react-navigation/stack@7.0.0-alpha.18) (2024-03-22)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.16...@react-navigation/stack@7.0.0-alpha.17) (2024-03-20)

### Features

* add getStateForRouteNamesChange to all navigators and mark it as unstable ([4edbb07](https://github.com/react-navigation/react-navigation/commit/4edbb071163742b60499178271fd3e3e92fb4002)) - by @satya164

# [7.0.0-alpha.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.15...@react-navigation/stack@7.0.0-alpha.16) (2024-03-14)

### Features

* automatically infer types for navigation in options, listeners etc. ([#11883](https://github.com/react-navigation/react-navigation/issues/11883)) ([c54baf1](https://github.com/react-navigation/react-navigation/commit/c54baf14640e567be10cb8a5f68e5cbf0b35f120)) - by @satya164

# [7.0.0-alpha.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.14...@react-navigation/stack@7.0.0-alpha.15) (2024-03-10)

### Features

* add a type for options arguments ([8e719e0](https://github.com/react-navigation/react-navigation/commit/8e719e0faefbd1eed9f7122a3d8e2c617d5f8254)) - by @satya164

# [7.0.0-alpha.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.13...@react-navigation/stack@7.0.0-alpha.14) (2024-03-09)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.12...@react-navigation/stack@7.0.0-alpha.13) (2024-03-08)

### Features

* add animation option for js stack ([#11854](https://github.com/react-navigation/react-navigation/issues/11854)) ([1843675](https://github.com/react-navigation/react-navigation/commit/18436751750ddb26671a07b04451ef16355f6792)) - by @groot007
* make all screens after presentation: 'modal' as modal unless specified ([#11860](https://github.com/react-navigation/react-navigation/issues/11860)) ([f16216c](https://github.com/react-navigation/react-navigation/commit/f16216c65740c2795cdef6c2249edffa9e9416ae)) - by @groot007

# [7.0.0-alpha.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.11...@react-navigation/stack@7.0.0-alpha.12) (2024-03-04)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.10...@react-navigation/stack@7.0.0-alpha.11) (2024-02-24)

### Bug Fixes

* fix peer dependency versions ([4b93b63](https://github.com/react-navigation/react-navigation/commit/4b93b6335ce180fe879f9fbe8f2400426b5484fb)) - by @

# [7.0.0-alpha.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.9...@react-navigation/stack@7.0.0-alpha.10) (2024-02-23)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.8...@react-navigation/stack@7.0.0-alpha.9) (2024-02-23)

### Bug Fixes

* don't remount native views on focus change on the new architecture ([#11806](https://github.com/react-navigation/react-navigation/issues/11806)) ([aa518f1](https://github.com/react-navigation/react-navigation/commit/aa518f1171d6f4caafa2fe5675eefe55b586fd28)) - by @j-piasecki
* handle failed gesture state to clear interaction ([#11465](https://github.com/react-navigation/react-navigation/issues/11465)) ([1b56cb5](https://github.com/react-navigation/react-navigation/commit/1b56cb5576599be1e3758b6d898b38c2fedefc37)) - by @Iltimirov

### Features

* add a HeaderButton component to elements ([d8de228](https://github.com/react-navigation/react-navigation/commit/d8de228bafc9408855bdfdfcd48bbb10195501fb)) - by @satya164
* add autoHideHomeIndicator option for stack ([#11415](https://github.com/react-navigation/react-navigation/issues/11415)) ([8367758](https://github.com/react-navigation/react-navigation/commit/8367758824590b9affd07ce651f8020aca2a88f6)) - by @mortend

# [7.0.0-alpha.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.7...@react-navigation/stack@7.0.0-alpha.8) (2024-01-17)

### Bug Fixes

* remove optional check in CardContainer ([#11772](https://github.com/react-navigation/react-navigation/issues/11772)) ([e73551a](https://github.com/react-navigation/react-navigation/commit/e73551a237f36e3242b329b71322e22d0190af74)) - by @osdnk
* remove unnecessary check in the stack navigator ([#11747](https://github.com/react-navigation/react-navigation/issues/11747)) ([02ec21b](https://github.com/react-navigation/react-navigation/commit/02ec21b0b5660ff17758b596852a7d7dc06ff69e)), closes [#11733](https://github.com/react-navigation/react-navigation/issues/11733) - by @osdnk

### Features

* add layout and screenLayout props for screens ([#11741](https://github.com/react-navigation/react-navigation/issues/11741)) ([2dc2178](https://github.com/react-navigation/react-navigation/commit/2dc217827a1caa615460563973d3d658be372b29)) - by @satya164
* add retaining of the screen to the stack navigator ([#11765](https://github.com/react-navigation/react-navigation/issues/11765)) ([7fe82f7](https://github.com/react-navigation/react-navigation/commit/7fe82f7217d9d146e5a127bcac921f0c90e2059f)), closes [#11758](https://github.com/react-navigation/react-navigation/issues/11758) - by @osdnk
* preloading for stack navigator ([#11733](https://github.com/react-navigation/react-navigation/issues/11733)) ([14fa6df](https://github.com/react-navigation/react-navigation/commit/14fa6dfa4484cf2784f0e5cd0d06252fdf8a4ba5)), closes [#11702](https://github.com/react-navigation/react-navigation/issues/11702) [#11727](https://github.com/react-navigation/react-navigation/issues/11727) - by @osdnk
* preloading in routers  ([382d6e6](https://github.com/react-navigation/react-navigation/commit/382d6e6f3312630b34332b1ae7d4bd7bf9b4ee60)) - by @osdnk

# [7.0.0-alpha.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.6...@react-navigation/stack@7.0.0-alpha.7) (2023-11-17)

### Bug Fixes

* update peer dependencies when publishing ([c440703](https://github.com/react-navigation/react-navigation/commit/c44070310f875e488708f2a6c52ffddcea05b0e6)) - by @

# [7.0.0-alpha.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.5...@react-navigation/stack@7.0.0-alpha.6) (2023-11-12)

### Bug Fixes

* workaround for mChrome empty space when navigating after address bar collapses ([#11366](https://github.com/react-navigation/react-navigation/issues/11366)) ([d638454](https://github.com/react-navigation/react-navigation/commit/d638454bec9e3f3bba3a808e13d7d1ae1be26acb)) - by @BeeMargarida

### Features

* add a layout prop for navigators ([#11614](https://github.com/react-navigation/react-navigation/issues/11614)) ([1f51190](https://github.com/react-navigation/react-navigation/commit/1f511904b9437d1451557147e72962859e97b1ae)) - by @satya164
* add API for unhandled linking ([#11672](https://github.com/react-navigation/react-navigation/issues/11672)) ([5758b26](https://github.com/react-navigation/react-navigation/commit/5758b2615e70ce4943b23ead0227507c63b11c7c)) - by @osdnk

# [7.0.0-alpha.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.4...@react-navigation/stack@7.0.0-alpha.5) (2023-09-25)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.3...@react-navigation/stack@7.0.0-alpha.4) (2023-09-13)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.2...@react-navigation/stack@7.0.0-alpha.3) (2023-09-07)

### Bug Fixes

* Allow to use `PlatformColor` in the theme ([#11570](https://github.com/react-navigation/react-navigation/issues/11570)) ([64734e7](https://github.com/react-navigation/react-navigation/commit/64734e7bc0d7f203d8e5db6abcc9a88157a5f16c)) - by @retyui

* feat!: add a direction prop to NavigationContainer to specify rtl (#11393) ([8309636](https://github.com/react-navigation/react-navigation/commit/830963653fb5a489d02f1503222629373319b39e)), closes [#11393](https://github.com/react-navigation/react-navigation/issues/11393) - by @satya164

### Features

* add shifting animation to bottom-tabs and various fixes ([#11581](https://github.com/react-navigation/react-navigation/issues/11581)) ([6d93c2d](https://github.com/react-navigation/react-navigation/commit/6d93c2da661e1991f6e60f25abf137110a005509)) - by @satya164

### BREAKING CHANGES

* Previously the navigators tried to detect RTL automatically and adjust the UI. However this is problematic since we cannot detect RTL in all cases (e.g. on Web).

This adds an optional `direction` prop to `NavigationContainer` instead so that user can specify when React Navigation's UI needs to be adjusted for RTL. It defaults to the value from `I18nManager` on native platforms, however it needs to be explicitly passed for Web.

# [7.0.0-alpha.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.1...@react-navigation/stack@7.0.0-alpha.2) (2023-06-22)

**Note:** Version bump only for package @react-navigation/stack

# [7.0.0-alpha.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@7.0.0-alpha.0...@react-navigation/stack@7.0.0-alpha.1) (2023-03-01)

### Bug Fixes

* fix paths in sourcemap files ([368e069](https://github.com/react-navigation/react-navigation/commit/368e0691b9fb07d4b1cbe71cfe4c2f40512f93ad)) - by @satya164

# [7.0.0-alpha.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.3.5...@react-navigation/stack@7.0.0-alpha.0) (2023-02-17)

### Bug Fixes

* Check cardStyleInterpolator name to allow for custom animation as well ([#11209](https://github.com/react-navigation/react-navigation/issues/11209)) ([c733ad5](https://github.com/react-navigation/react-navigation/commit/c733ad53c9209b9b2a778a427458bcbdeb45c841)), closes [/github.com/react-navigation/react-navigation/blob/main/packages/stack/src/TransitionConfigs/CardStyleInterpolators.tsx#L93](https://github.com//github.com/react-navigation/react-navigation/blob/main/packages/stack/src/TransitionConfigs/CardStyleInterpolators.tsx/issues/L93) [/github.com/react-navigation/react-navigation/blob/main/packages/stack/src/views/Stack/Card.tsx#L580](https://github.com//github.com/react-navigation/react-navigation/blob/main/packages/stack/src/views/Stack/Card.tsx/issues/L580) - by @drager
* fix checking for modal presentation in stack ([639d6c8](https://github.com/react-navigation/react-navigation/commit/639d6c8a511d76e48d44ed7260aedb865054c556)) - by @satya164

* refactor!: drop support for key property in navigate ([61c53bb](https://github.com/react-navigation/react-navigation/commit/61c53bb1836a47083b3b5ea0f4fddba6081885f2)) - by @satya164

### BREAKING CHANGES

* Previously, you could specify a route `key` to navigate to, e.g.
`navigation.navigate({ key: 'someuniquekey' })`. It's problematic since `key` is an internal
implementation detail and created by the library internally - which makes it weird to use. None
of the other actions support such usage either.

In addition, we have already added a better API (`getId`) which can be used for similar use
cases - and gives users full control since they provide the ID and it's not autogenerated by the
library.

So this change removes the `key` option from the `navigate` action.

## [6.3.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.3.4...@react-navigation/stack@6.3.5) (2022-11-21)

### Bug Fixes

* add accessibility props to NativeStack screens ([#11022](https://github.com/react-navigation/react-navigation/issues/11022)) ([3ab05af](https://github.com/react-navigation/react-navigation/commit/3ab05afeb6412b8e5566270442ac14a463136620))
* supersede Platform.isTVOS for Platform.isTV ([#10973](https://github.com/react-navigation/react-navigation/issues/10973)) ([1846de6](https://github.com/react-navigation/react-navigation/commit/1846de6bd8247992286d39ee76e65f27debb1754))

## [6.3.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.3.3...@react-navigation/stack@6.3.4) (2022-11-04)

### Bug Fixes

* migrate setNativeProps to state ([#10968](https://github.com/react-navigation/react-navigation/issues/10968)) ([37d5440](https://github.com/react-navigation/react-navigation/commit/37d5440d50a5b081910e436d1ba4b30f5797f81d)), closes [#10871](https://github.com/react-navigation/react-navigation/issues/10871)

## [6.3.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.3.2...@react-navigation/stack@6.3.3) (2022-10-27)

### Reverts

* Revert "fix: add additional check before running `animate` in `componentDidUpdate` (#10871)" ([7d8f748](https://github.com/react-navigation/react-navigation/commit/7d8f748b1b2a87a3a7873e2c793966422ee1a370)), closes [#10871](https://github.com/react-navigation/react-navigation/issues/10871)

## [6.3.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.3.1...@react-navigation/stack@6.3.2) (2022-10-05)

### Bug Fixes

* add additional check before running `animate` in `componentDidUpdate` ([#10871](https://github.com/react-navigation/react-navigation/issues/10871)) ([b9fb2d1](https://github.com/react-navigation/react-navigation/commit/b9fb2d14c6d518793f11e5f38a79f8a83baf3c09)), closes [#10767](https://github.com/react-navigation/react-navigation/issues/10767)

## [6.3.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.3.0...@react-navigation/stack@6.3.1) (2022-09-19)

### Reverts

* Revert "fix: migrate off setNativeProps (#10767)" ([dfc11e7](https://github.com/react-navigation/react-navigation/commit/dfc11e7616c99182ac736c7d22d3d67c4075c211)), closes [#10767](https://github.com/react-navigation/react-navigation/issues/10767)

# [6.3.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.2.2...@react-navigation/stack@6.3.0) (2022-09-16)

### Bug Fixes

* export `PanGestureHandlerGestureEvent` as a type ([#10814](https://github.com/react-navigation/react-navigation/issues/10814)) ([f3d06ba](https://github.com/react-navigation/react-navigation/commit/f3d06ba7c3393fcdb30ad5cf515565cd5196b3f0))
* fix previous screen detach too early ([#10813](https://github.com/react-navigation/react-navigation/issues/10813)) ([a6b7d10](https://github.com/react-navigation/react-navigation/commit/a6b7d102d7ad6a6ee672ee5210cce4c1715acf03)), closes [/github.com/software-mansion/react-native-screens/issues/1285#issuecomment-1026928610](https://github.com//github.com/software-mansion/react-native-screens/issues/1285/issues/issuecomment-1026928610)
* migrate off setNativeProps ([#10767](https://github.com/react-navigation/react-navigation/issues/10767)) ([495b28f](https://github.com/react-navigation/react-navigation/commit/495b28f06a8c89ce9bc2c812bcbb2374366b9aaf)), closes [#10720](https://github.com/react-navigation/react-navigation/issues/10720)
* replace deprecated I18nManager.isRTL with 18nManager.getConstants().isRTL ([#10547](https://github.com/react-navigation/react-navigation/issues/10547)) ([50b88d4](https://github.com/react-navigation/react-navigation/commit/50b88d40496a04f613073c63119b21a104ec9bc2))

### Features

* add freezeOnBlur prop  ([#10834](https://github.com/react-navigation/react-navigation/issues/10834)) ([e13b4d9](https://github.com/react-navigation/react-navigation/commit/e13b4d9341362512ba4bf921a17552f3be8735c1))

## [6.2.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.2.2...@react-navigation/stack@6.2.3) (2022-08-24)

### Bug Fixes

* replace deprecated I18nManager.isRTL with 18nManager.getConstants().isRTL ([#10547](https://github.com/react-navigation/react-navigation/issues/10547)) ([50b88d4](https://github.com/react-navigation/react-navigation/commit/50b88d40496a04f613073c63119b21a104ec9bc2))

## [6.2.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.2.1...@react-navigation/stack@6.2.2) (2022-07-05)

### Bug Fixes

* ensure same @types/react version in repo ([#10663](https://github.com/react-navigation/react-navigation/issues/10663)) ([e662465](https://github.com/react-navigation/react-navigation/commit/e6624653fbbd931158dbebd17142abf9637205b6)), closes [#10655](https://github.com/react-navigation/react-navigation/issues/10655)

## [6.2.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.2.0...@react-navigation/stack@6.2.1) (2022-04-01)

**Note:** Version bump only for package @react-navigation/stack

# [6.2.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.1.1...@react-navigation/stack@6.2.0) (2022-04-01)

### Bug Fixes

* fix type errors when passing animated styles to header ([9058b1c](https://github.com/react-navigation/react-navigation/commit/9058b1c22f4fc1358c72d67150a0e3f37ff802e7))

### Features

* add an ID prop to navigators ([4e4935a](https://github.com/react-navigation/react-navigation/commit/4e4935ac2584bc1a00209609cc026fa73e12c10a))

## [6.1.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.1.0...@react-navigation/stack@6.1.1) (2022-02-02)

**Note:** Version bump only for package @react-navigation/stack

# [6.1.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.11...@react-navigation/stack@6.1.0) (2022-01-29)

### Bug Fixes

* fix transparent modal when another screen is pushed on top ([9d2d14b](https://github.com/react-navigation/react-navigation/commit/9d2d14b78ec074f0871a005950d9671a9adfe2de)), closes [#10298](https://github.com/react-navigation/react-navigation/issues/10298)
* make stack navigator work with latest gesture handler ([#10270](https://github.com/react-navigation/react-navigation/issues/10270)) ([5a19877](https://github.com/react-navigation/react-navigation/commit/5a19877080bd406c5657a096f3c8cd5573316718))

### Features

* **native-stack:** export NativeStackView to support custom routers on native-stack ([#10260](https://github.com/react-navigation/react-navigation/issues/10260)) ([7b761f1](https://github.com/react-navigation/react-navigation/commit/7b761f1cc069ca68b96b5155be726024a345346f))
* pass canGoBack to headerRight ([82a1669](https://github.com/react-navigation/react-navigation/commit/82a16690973a7935939a25a66d5786955b6c8ba7))

## [6.0.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.10...@react-navigation/stack@6.0.11) (2021-10-12)

**Note:** Version bump only for package @react-navigation/stack

## [6.0.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.9...@react-navigation/stack@6.0.10) (2021-10-09)

**Note:** Version bump only for package @react-navigation/stack

## [6.0.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.8...@react-navigation/stack@6.0.9) (2021-09-26)

**Note:** Version bump only for package @react-navigation/stack

## [6.0.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.7...@react-navigation/stack@6.0.8) (2021-09-26)

### Bug Fixes

* stop  animations on unmount/cleanup ([5fb5f41](https://github.com/react-navigation/react-navigation/commit/5fb5f41eb6cf86ebe2f7777d6c98bda16ce71b5b))

## [6.0.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.6...@react-navigation/stack@6.0.7) (2021-08-17)

### Bug Fixes

* add check when accessing next descriptor ([#9824](https://github.com/react-navigation/react-navigation/issues/9824)) ([5bcf79c](https://github.com/react-navigation/react-navigation/commit/5bcf79c722e62403d8398874fd0b2b673f840aa3))
* don't try to parse header tint color if not a string ([ece03d7](https://github.com/react-navigation/react-navigation/commit/ece03d7177731d8eabcc082f34b674776dffc4ce)), closes [#9822](https://github.com/react-navigation/react-navigation/issues/9822)
* fix status bar handling with modal presentation ([651d8e5](https://github.com/react-navigation/react-navigation/commit/651d8e5726d9abab2e4572bd0fad550e926cc9b4))
* handle statusbar height for stack nested in modal. closes [#9790](https://github.com/react-navigation/react-navigation/issues/9790) ([f54cafb](https://github.com/react-navigation/react-navigation/commit/f54cafbb3333763ec86e4b2cab46cbb2ae99b627))

## [6.0.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.5...@react-navigation/stack@6.0.6) (2021-08-11)

**Note:** Version bump only for package @react-navigation/stack

## [6.0.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.4...@react-navigation/stack@6.0.5) (2021-08-11)

### Bug Fixes

* show deprecation warning for 'keyboardHandlingEnabled' prop ([8c89c45](https://github.com/react-navigation/react-navigation/commit/8c89c45be45396024271bb5be33760e3c06a09be))

## [6.0.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.3...@react-navigation/stack@6.0.4) (2021-08-09)

### Bug Fixes

* avoid overflowing long titles ([bacdbbd](https://github.com/react-navigation/react-navigation/commit/bacdbbdd7c5df73b84aa1ed8c0329c9525d0fdba))
* pass all props to custom headerLeft ([#9806](https://github.com/react-navigation/react-navigation/issues/9806)) ([22799fc](https://github.com/react-navigation/react-navigation/commit/22799fc96ee689cad97ee051c24816f86ac912d1)), closes [#9805](https://github.com/react-navigation/react-navigation/issues/9805)
* pass onlayout to headerTitle ([#9808](https://github.com/react-navigation/react-navigation/issues/9808)) ([a79ce57](https://github.com/react-navigation/react-navigation/commit/a79ce57aa7f9be3a47a09728e920c0d4a805f5aa))

## [6.0.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.2...@react-navigation/stack@6.0.3) (2021-08-09)

### Bug Fixes

* consider all next headers instead of immediate next ([4d8a7ee](https://github.com/react-navigation/react-navigation/commit/4d8a7ee7e422c9d0eeb7aa0557eb288ef62b1f30)), closes [#9797](https://github.com/react-navigation/react-navigation/issues/9797)

## [6.0.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.1...@react-navigation/stack@6.0.2) (2021-08-07)

**Note:** Version bump only for package @react-navigation/stack

## [6.0.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0...@react-navigation/stack@6.0.1) (2021-08-03)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.29...@react-navigation/stack@6.0.0) (2021-08-01)

### Bug Fixes

* match native iOS header height in stack ([51b636d](https://github.com/react-navigation/react-navigation/commit/51b636d7268fc05a8a9aca9e6aad0161674f238e))
* remove dep on react-native-iphonex-helper ([8a95fb5](https://github.com/react-navigation/react-navigation/commit/8a95fb588bd1f8a72fc4ef4e847f06e103ed55fe))

### Features

* expose header height in native-stack ([#9774](https://github.com/react-navigation/react-navigation/issues/9774)) ([20abccd](https://github.com/react-navigation/react-navigation/commit/20abccda0d5074f61b2beb555b881a2087d27bb0))

# [6.0.0-next.29](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.28...@react-navigation/stack@6.0.0-next.29) (2021-07-16)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.28](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.27...@react-navigation/stack@6.0.0-next.28) (2021-07-16)

### Bug Fixes

* update isClosing in stack card ([#9738](https://github.com/react-navigation/react-navigation/issues/9738)) ([ee12690](https://github.com/react-navigation/react-navigation/commit/ee12690a823694fa19a3216f97676870f2999719))

# [6.0.0-next.27](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.25...@react-navigation/stack@6.0.0-next.27) (2021-07-01)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.26](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.25...@react-navigation/stack@6.0.0-next.26) (2021-06-10)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.25](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.24...@react-navigation/stack@6.0.0-next.25) (2021-06-01)

### Bug Fixes

* tweak android q animation ([7d74bd7](https://github.com/react-navigation/react-navigation/commit/7d74bd73a7333f0b1373a05c9f06e981556feed2))

# [6.0.0-next.24](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.23...@react-navigation/stack@6.0.0-next.24) (2021-05-29)

### Bug Fixes

* remove card shadow from default animation ([70f4fe2](https://github.com/react-navigation/react-navigation/commit/70f4fe2ffaa219a2a72e0ae25b7d5d007948c2a4)), closes [#9569](https://github.com/react-navigation/react-navigation/issues/9569)

# [6.0.0-next.23](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.22...@react-navigation/stack@6.0.0-next.23) (2021-05-29)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.22](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.21...@react-navigation/stack@6.0.0-next.22) (2021-05-27)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.21](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.20...@react-navigation/stack@6.0.0-next.21) (2021-05-26)

### Features

* add screenListeners prop on navigators similar to screenOptions ([cde44a5](https://github.com/react-navigation/react-navigation/commit/cde44a5785444a121aa08f94af9f8fe4fc89910a))

# [6.0.0-next.20](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.19...@react-navigation/stack@6.0.0-next.20) (2021-05-25)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.19](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.18...@react-navigation/stack@6.0.0-next.19) (2021-05-24)

### Bug Fixes

* make transparent modal work with modal presentation ([c90bff0](https://github.com/react-navigation/react-navigation/commit/c90bff08d54d1c7151737a43d4f73abe7d364366))

# [6.0.0-next.18](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.17...@react-navigation/stack@6.0.0-next.18) (2021-05-23)

### Features

* add 'transparentModal' presentation to JS stack ([3d14740](https://github.com/react-navigation/react-navigation/commit/3d147401e8ca98ec2d51d5e11c09cd1271d448d1))
* enable ipad trackpad two finger back gesture ([4ec6acd](https://github.com/react-navigation/react-navigation/commit/4ec6acdf97ba1b34f25b22af5262798a36b9020a))

# [6.0.0-next.17](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.16...@react-navigation/stack@6.0.0-next.17) (2021-05-16)

### Bug Fixes

* don't allow overriding gestureEnabled on first screen ([9f00d60](https://github.com/react-navigation/react-navigation/commit/9f00d60bdb7d6f02996f4fa7dfc09b890ebe22f9))
* fix broken animation on first screen ([e5238f6](https://github.com/react-navigation/react-navigation/commit/e5238f608472319165e5570f41144be5549e0df1))

# [6.0.0-next.16](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.15...@react-navigation/stack@6.0.0-next.16) (2021-05-16)

### Bug Fixes

* don't enable animation & gestures for first screen ([6ebe082](https://github.com/react-navigation/react-navigation/commit/6ebe0824df3df4973190428a14286aab0d14d3c1))
* make stack screens accessible on web without screens ([22b7c3f](https://github.com/react-navigation/react-navigation/commit/22b7c3f6c18a73fc55b0289b91b8d3a96f5be29c))
* move keyboardHandlingEnabled to screen options ([82900cc](https://github.com/react-navigation/react-navigation/commit/82900cceffa3e338b7b93e831f9ba6cbb3784815))

# [6.0.0-next.15](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.14...@react-navigation/stack@6.0.0-next.15) (2021-05-10)

### Bug Fixes

* add a deprecation warning for mode prop in stack ([a6e4981](https://github.com/react-navigation/react-navigation/commit/a6e498170f59648190fa5513e273ca523e56c5d5))

### Features

* return a NavigationContent component from useNavigationBuilder ([1179d56](https://github.com/react-navigation/react-navigation/commit/1179d56c5008270753feef41acdc1dbd2191efcf))

# [6.0.0-next.14](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.13...@react-navigation/stack@6.0.0-next.14) (2021-05-09)

### Bug Fixes

* fix modal animation not being set properly ([08e74af](https://github.com/react-navigation/react-navigation/commit/08e74af54529582dcbc8d91e77bfed70f006f00d))

# [6.0.0-next.13](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.12...@react-navigation/stack@6.0.0-next.13) (2021-05-09)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.12](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.11...@react-navigation/stack@6.0.0-next.12) (2021-05-09)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.11](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.10...@react-navigation/stack@6.0.0-next.11) (2021-05-09)

### Bug Fixes

* enable screens only on supported platforms ([#9494](https://github.com/react-navigation/react-navigation/issues/9494)) ([8da4c58](https://github.com/react-navigation/react-navigation/commit/8da4c58065607d44e9dc1ad8943e09537598dcd7))
* make sure disabling react-native-screens works ([a369ba3](https://github.com/react-navigation/react-navigation/commit/a369ba36451ddc2bb5b247e61b725bce1e3fb5e5))

### Code Refactoring

* drop mode prop in favor of animationPresentation option ([9ac709e](https://github.com/react-navigation/react-navigation/commit/9ac709ea1e5a63c3a48abfa334ff6a6925095a72))

### Features

* automatically set headerMode if it's modal presentation style ([4bb0b43](https://github.com/react-navigation/react-navigation/commit/4bb0b43f1a0f27c96843415de6eaa37edebfb561))
* support mixing regular and modal presentation in same stack ([60fa3b9](https://github.com/react-navigation/react-navigation/commit/60fa3b9be976a73a5b04b632b4b37672674c956b))

### BREAKING CHANGES

* This drops the mode prop on the navigator in favor of a per-screen option animationPresentation

# [6.0.0-next.10](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.9...@react-navigation/stack@6.0.0-next.10) (2021-05-01)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.9](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.8...@react-navigation/stack@6.0.0-next.9) (2021-04-08)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.8](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.7...@react-navigation/stack@6.0.0-next.8) (2021-03-22)

### Features

* add a Background component ([cbaabc1](https://github.com/react-navigation/react-navigation/commit/cbaabc1288e780698e499a00b9ca06ab9746a0da))

# [6.0.0-next.7](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.6...@react-navigation/stack@6.0.0-next.7) (2021-03-22)

### Code Refactoring

* make gestureResponseDistance a number ([48851c9](https://github.com/react-navigation/react-navigation/commit/48851c9ebdcf1b835bbcb673adeb88e56b989443))

### BREAKING CHANGES

* now we need to pass a number instead of an object

# [6.0.0-next.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.5...@react-navigation/stack@6.0.0-next.6) (2021-03-14)

**Note:** Version bump only for package @react-navigation/stack

# [6.0.0-next.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.4...@react-navigation/stack@6.0.0-next.5) (2021-03-14)

### Bug Fixes

* consider header colors when managing statusbar ([3ad2bcb](https://github.com/react-navigation/react-navigation/commit/3ad2bcbaf85996ce0d5e1e961081978a32448899))
* consider header colors when managing statusbar ([faee245](https://github.com/react-navigation/react-navigation/commit/faee245d2ec8c59f9e9033d96ae21c5e60d95ba6))

### Code Refactoring

* move headerMode to options ([aacc1b5](https://github.com/react-navigation/react-navigation/commit/aacc1b525d86f0e0b1bad8016fd85e82024f16e9))

### BREAKING CHANGES

* headerMode is now moved to options instead of props

# [6.0.0-next.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.3...@react-navigation/stack@6.0.0-next.4) (2021-03-12)

### Bug Fixes

* add special statusbar handling to modal presentation ([a204edd](https://github.com/react-navigation/react-navigation/commit/a204edd012060f0816eddee7a093183aa379d049))

# [6.0.0-next.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.2...@react-navigation/stack@6.0.0-next.3) (2021-03-12)

### Features

* export drawer button ([2c8401d](https://github.com/react-navigation/react-navigation/commit/2c8401d5cb347d37c96e5b30f8ad05c17fd22ea4))
* return nearest parent header height for useHeaderHeight ([24b3f73](https://github.com/react-navigation/react-navigation/commit/24b3f739da4b8af8dca77d92c72cfdaa762e564a))

# [6.0.0-next.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0-next.1...@react-navigation/stack@6.0.0-next.2) (2021-03-11)

### Bug Fixes

* respect headerStatusBarHeight option in Stack ([#9405](https://github.com/react-navigation/react-navigation/issues/9405)) ([8a6511c](https://github.com/react-navigation/react-navigation/commit/8a6511c491b2affbe378d720e613a3e3041ca9c2))

# [6.0.0-next.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@6.0.0...@react-navigation/stack@6.0.0-next.1) (2021-03-10)

### Bug Fixes

* fix peer dep versions ([72f90b5](https://github.com/react-navigation/react-navigation/commit/72f90b50d27eda1315bb750beca8a36f26dafe17))
* remove use of deprecated currentlyFocusedField ([038eb87](https://github.com/react-navigation/react-navigation/commit/038eb87c42564f9d733e6870826726d3fb0adaee))

# [6.0.0-next.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.6...@react-navigation/stack@6.0.0-next.0) (2021-03-09)

### Bug Fixes

* add missing helper types in descriptors ([21a1154](https://github.com/react-navigation/react-navigation/commit/21a11543bf41c4559c2570d5accc0bbb3b67eb8d))
* drop support for headerMode='none' ([0c55803](https://github.com/react-navigation/react-navigation/commit/0c55803b32c5d20216b91aa7a355b042051ae638))
* drop usage of Dimensions in favor of metrics from safe-area-context ([12b893d](https://github.com/react-navigation/react-navigation/commit/12b893d7ca8cdb726b973972797658ac9c7d17d7))
* enable detachInactiveScreens by default on web for better a11y ([4954d6a](https://github.com/react-navigation/react-navigation/commit/4954d6aae3cdbb5855d44ff17d80d16b81fb224e))
* fix initial metrics on server ([69d333f](https://github.com/react-navigation/react-navigation/commit/69d333f6c23e0c37eaf4d3f8b413e8f96d6827f8))
* fix transparent modal on web ([c345ef1](https://github.com/react-navigation/react-navigation/commit/c345ef1d0b82e0c0ec1ebd3a3ced4ed5b5e835e4))
* force dismiss keyboard if there was no gesture ([14ac256](https://github.com/react-navigation/react-navigation/commit/14ac256af363f3bc10ba6cb4ce2d67f347116042)), closes [#9078](https://github.com/react-navigation/react-navigation/issues/9078)

### Code Refactoring

* don't use absolute position for header ([79a85a4](https://github.com/react-navigation/react-navigation/commit/79a85a431ce0859ae35a13858b23c3919795e560))
* don't use deprecated APIs from react-native-safe-area-context ([ddf27bf](https://github.com/react-navigation/react-navigation/commit/ddf27bf41a2efc5d1573aad0f8fe6c27a98c32b3))
* simplify props for stack and drawer headers ([4cad132](https://github.com/react-navigation/react-navigation/commit/4cad132c2c3daa6370a6916977f1f1db0036d4e4))

### Features

* add a slide animation for modals on Android ([6f326cf](https://github.com/react-navigation/react-navigation/commit/6f326cf0c5098a722176aedd2051d29e12c95592))
* add an option to specify default options for the navigator ([c85f2ff](https://github.com/react-navigation/react-navigation/commit/c85f2ff47a2b3d403a3cbe993b46d04914358ba5))
* add pressColor and pressOpacity props to drawerItem ([#8834](https://github.com/react-navigation/react-navigation/issues/8834)) ([52dbe4b](https://github.com/react-navigation/react-navigation/commit/52dbe4bd6663430745b07ea379d44d4d4f2944a0))
* don't hardcode header back test ID ([22a8afa](https://github.com/react-navigation/react-navigation/commit/22a8afac7454cfb776a550abc498ed47c299261d))
* export TransitionPreset for custom TransitionPresets ([#9173](https://github.com/react-navigation/react-navigation/issues/9173)) ([6c3cccf](https://github.com/react-navigation/react-navigation/commit/6c3cccf87715ccb0a6c4bca6544f29c1b30e9611))
* initial implementation of @react-navigation/elements ([07ba7a9](https://github.com/react-navigation/react-navigation/commit/07ba7a96870efdb8acf99eb82ba0b1d3eac90bab))
* use modal presentation style for modals on iOS by default ([8a63f19](https://github.com/react-navigation/react-navigation/commit/8a63f193bf26c35546aa45af01d89b7a7216657d))

### BREAKING CHANGES

* We now use flexbox for header elements which could break some existing style code which relied on absolute positioning.
* Now the back button test ID can be customized using headerBackTestID option
* We now require newer versions of safe area context library.
* Previously, the stack header accepted scene and previous scene which contained things such as descriptor, navigation prop, progress etc. The commit simplifies them to pass `route`, `navigation`, `options` and `progress` directly to the header. Similaryly, the `previous` argument now contains `options`, `route` and `progress`.
* The header can be hidden with `headerShown: false` already. Using both `headerMode='none'` and `headerShown` together causes confusion. So it's time to drop the unnecessary `headerMode` prop.

## [5.12.6](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.5...@react-navigation/stack@5.12.6) (2020-11-10)

### Bug Fixes

* make sure inactive screen don't increase scroll area on web ([da35085](https://github.com/react-navigation/react-navigation/commit/da35085f1e3440f26eea800c892c88aec64d072f))

## [5.12.5](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.4...@react-navigation/stack@5.12.5) (2020-11-09)

**Note:** Version bump only for package @react-navigation/stack

## [5.12.4](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.3...@react-navigation/stack@5.12.4) (2020-11-08)

### Bug Fixes

* don't hide child header automatically in stack ([8f0efc8](https://github.com/react-navigation/react-navigation/commit/8f0efc8db534297a95ea8a2bcb6d2e387c1fea53))

## [5.12.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.2...@react-navigation/stack@5.12.3) (2020-11-04)

### Bug Fixes

* android textinput gets blurred after navigating back ([1169ed0](https://github.com/react-navigation/react-navigation/commit/1169ed0946df609cb7e5c52c4bdda0aa91b5737f))
* disable react-native-screens on iOS for older versions ([ce7d20e](https://github.com/react-navigation/react-navigation/commit/ce7d20e3366415b07a537e01ee0b17ce7e72cad6))

## [5.12.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.1...@react-navigation/stack@5.12.2) (2020-11-04)

**Note:** Version bump only for package @react-navigation/stack

## [5.12.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.12.0...@react-navigation/stack@5.12.1) (2020-11-03)

**Note:** Version bump only for package @react-navigation/stack

# [5.12.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.11.1...@react-navigation/stack@5.12.0) (2020-11-03)

### Features

* add a headerBackAccessibilityLabel option in stack ([c326c10](https://github.com/react-navigation/react-navigation/commit/c326c106f9a2492ff45bdc8da9bfbc404e48786a)), closes [#9016](https://github.com/react-navigation/react-navigation/issues/9016)

## [5.11.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.11.0...@react-navigation/stack@5.11.1) (2020-10-30)

**Note:** Version bump only for package @react-navigation/stack

# [5.11.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.10.0...@react-navigation/stack@5.11.0) (2020-10-28)

### Features

* enable react-native-screens in Stack by default on iOS ([45dbe5c](https://github.com/react-navigation/react-navigation/commit/45dbe5c40ebc66c62593b3fad35cff3048b888a4))

# [5.10.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.9.3...@react-navigation/stack@5.10.0) (2020-10-24)

### Bug Fixes

* add missing check for parent header when calculating height ([da91cec](https://github.com/react-navigation/react-navigation/commit/da91cec941e7dec352ba1910901904d769c9f3a3))
* don't set statusbar height on nested header by default ([38e17aa](https://github.com/react-navigation/react-navigation/commit/38e17aae939974b47fe5c77d8670b9a4544250f2))
* fix header buttons not pressable when headerTransparent=true & headerMode=float ([#8804](https://github.com/react-navigation/react-navigation/issues/8804)) ([d6cac67](https://github.com/react-navigation/react-navigation/commit/d6cac6713a51e4de320fc1c7ece72a2b92241574)), closes [#8731](https://github.com/react-navigation/react-navigation/issues/8731)
* set needsOffscreenAlphaCompositing and update default android animation ([8ee0dda](https://github.com/react-navigation/react-navigation/commit/8ee0dda155b4cde43be1e58170e44823b54e7d4f)), closes [#8696](https://github.com/react-navigation/react-navigation/issues/8696)

### Features

* add optional screens per navigator ([#8805](https://github.com/react-navigation/react-navigation/issues/8805)) ([7196889](https://github.com/react-navigation/react-navigation/commit/7196889bf1218eb6a736d9475e33a909c2248c3b))
* improve types for navigation state ([#8980](https://github.com/react-navigation/react-navigation/issues/8980)) ([7dc2f58](https://github.com/react-navigation/react-navigation/commit/7dc2f5832e371473f3263c01ab39824eb9e2057d))
* update helper types to have navigator specific methods ([f51086e](https://github.com/react-navigation/react-navigation/commit/f51086edea42f2382dac8c6914aac8574132114b))

## [5.9.3](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.9.2...@react-navigation/stack@5.9.3) (2020-10-07)

**Note:** Version bump only for package @react-navigation/stack

## [5.9.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.9.1...@react-navigation/stack@5.9.2) (2020-09-28)

**Note:** Version bump only for package @react-navigation/stack

## [5.9.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.9.0...@react-navigation/stack@5.9.1) (2020-09-22)

### Bug Fixes

* add aria-level to HeaderTitle ([2d1da7e](https://github.com/react-navigation/react-navigation/commit/2d1da7ef2ffdb25f74e19e81b7e685fbb487f0c5))

# [5.9.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.8.0...@react-navigation/stack@5.9.0) (2020-08-04)

### Bug Fixes

* fix TouchableItem opacity on press on iOS ([40e2dba](https://github.com/react-navigation/react-navigation/commit/40e2dbaecffc43df41b7951f152bbcb4b7104bb1))

### Features

* add Windows and macOS support ([#8570](https://github.com/react-navigation/react-navigation/issues/8570)) ([8468c46](https://github.com/react-navigation/react-navigation/commit/8468c46cab01fe3bf0cf8a0ab978d16f4e78aca0))

# [5.8.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.7.1...@react-navigation/stack@5.8.0) (2020-07-28)

### Features

* allow style overrides for HeaderBackButton ([#8626](https://github.com/react-navigation/react-navigation/issues/8626)) ([486c3de](https://github.com/react-navigation/react-navigation/commit/486c3defd27592bf4170af4962a1c66f4710b17a))
* emit gesture navigation events from stack view ([#8524](https://github.com/react-navigation/react-navigation/issues/8524)) ([15f9b95](https://github.com/react-navigation/react-navigation/commit/15f9b9573e52666f88b0f917396496b03218f160))

## [5.7.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.7.0...@react-navigation/stack@5.7.1) (2020-07-19)

**Note:** Version bump only for package @react-navigation/stack

# [5.7.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.6.2...@react-navigation/stack@5.7.0) (2020-07-10)

### Features

* add a `beforeRemove` event ([6925e92](https://github.com/react-navigation/react-navigation/commit/6925e92dc3e9885e3f552ca5e5eb51ae1521e54e))

## [5.6.2](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.6.1...@react-navigation/stack@5.6.2) (2020-06-25)

**Note:** Version bump only for package @react-navigation/stack

## [5.6.1](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.6.0...@react-navigation/stack@5.6.1) (2020-06-25)

### Bug Fixes

* fix showing back button with headerMode=screen. fixes [#8508](https://github.com/react-navigation/react-navigation/issues/8508) ([fc95d7a](https://github.com/react-navigation/react-navigation/commit/fc95d7a256846b6a4fa999fbbe3fed2051972b42))

# [5.6.0](https://github.com/react-navigation/react-navigation/compare/@react-navigation/stack@5.5.1...@react-navigation/stack@5.6.0) (2020-06-24)

### Bug Fixes

* don't use deprecated currentlyFocusedField ([#8365](https://github.com/react-navigation/react-navigation/issues/8365)) ([35d6b9e](https://github.com/react-navigation/react-navigation/commit/35d6b9e3a4a28a59b3b11a67acbf7753d41705ae))
* fix screen disappearing on Android ([#8473](https://github.com/react-navigation/react-navigation/issues/8473)) ([962456b](https://github.com/react-navigation/react-navigation/commit/962456beb6ab0c8d5a075e4f268a22faf8b60dbb))
* workaround keyboard dismissing on focus ([37bbbbe](https://github.com/react-navigation/react-navigation/commit/37bbbbe8690d39b02c39ab12a610f83001b25455)), closes [#8414](https://github.com/react-navigation/react-navigation/issues/8414) [#8478](https://github.com/react-navigation/react-navigation/issues/8478)

### Features

* show back button in nested stack ([a2d649f](https://github.com/react-navigation/react-navigation/commit/a2d649faf124ac99fd333d4360e8068bb73675a6))

## [5.5.1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.5.0...@react-navigation/stack@5.5.1) (2020-06-08)

### Bug Fixes

* make sure the header is on top of the view ([1ae07af](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/1ae07af79660973f4342a5741a1a826bcc689832))

# [5.5.0](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.4.2...@react-navigation/stack@5.5.0) (2020-06-08)

### Bug Fixes

* fix blank screen with animationEnabled: false & headerShown: false ([9c06a92](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9c06a92d092af150d653c3a2f7fdccd28090bb14)), closes [#8391](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8391)
* ignore onOpen from route that wasn't closing ([1f27e4b](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/1f27e4b1f659e59ad15ecbf44b4fb0a80cae302f)), closes [#8257](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8257)
* pass gestureRef to PanGestureHandlerNative ([#8394](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8394)) ([c7e4bf9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/c7e4bf94e664563892cbdafccc108ad519ccec50))

### Features

* automatically hide header in nested stacks ([e0e0f79](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/e0e0f79793be552e5532cd0afe9444000d21341e))

## [5.4.2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.4.1...@react-navigation/stack@5.4.2) (2020-06-06)

### Bug Fixes

* relatively position float Header if !headerTransparent ([#8285](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8285)) ([78afbff](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/78afbffe976b14bb60666a2b1230127db0dc24f6))

## [5.4.1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.4.0...@react-navigation/stack@5.4.1) (2020-05-27)

### Bug Fixes

* allow HeaderBackground's subViews to be touchable ([#8317](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8317)) ([00c23f2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/00c23f2c9ed22fa4d010ffb427f2b52e061d8df4))
* fix type of style for various options ([9d822b9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9d822b95a6df797e2e63e481573e64ea7d0f9386))

# [5.4.0](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.9...@react-navigation/stack@5.4.0) (2020-05-23)

### Bug Fixes

* allow HeaderBackground's subViews to be touchable ([#8314](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8314)) ([021a911](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/021a9111d76b9b0d940c8829f7caebeb292fec2a))
* don't ignore previous header heights on layout update ([6dd45fc](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/6dd45fcff98a0c222d120d6f76a37130de45b92f))

### Features

* update react-native-safe-area-context to 1.0.0 ([#8182](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8182)) ([d62fbfe](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/d62fbfe255140f16b182e8b54b276a7c96f2aec6))

## [5.3.9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.8...@react-navigation/stack@5.3.9) (2020-05-20)

**Note:** Version bump only for package @react-navigation/stack

## [5.3.8](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.7...@react-navigation/stack@5.3.8) (2020-05-20)

**Note:** Version bump only for package @react-navigation/stack

## [5.3.7](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.6...@react-navigation/stack@5.3.7) (2020-05-16)

**Note:** Version bump only for package @react-navigation/stack

## [5.3.6](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.5...@react-navigation/stack@5.3.6) (2020-05-15)

### Bug Fixes

* reduce header title margin. fixes [#8267](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8267) ([d45dbe9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/d45dbe97dc6625c6a8e80b5e658ab5a95045e5e8))

## [5.3.5](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.4...@react-navigation/stack@5.3.5) (2020-05-14)

**Note:** Version bump only for package @react-navigation/stack

## [5.3.4](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.3...@react-navigation/stack@5.3.4) (2020-05-14)

**Note:** Version bump only for package @react-navigation/stack

## [5.3.3](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.2...@react-navigation/stack@5.3.3) (2020-05-11)

### Bug Fixes

* fix ios transitionspec settle time ([#8028](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8028)) ([dd7cff2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/dd7cff201608365a80f1d50a006df3e0d18e94a1))

## [5.3.2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.1...@react-navigation/stack@5.3.2) (2020-05-10)

**Note:** Version bump only for package @react-navigation/stack

## [5.3.1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.3.0...@react-navigation/stack@5.3.1) (2020-05-08)

### Bug Fixes

* fix building typescript definitions. closes [#8216](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8216) ([47a1229](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/47a12298378747edd2d22e54dc1c8677f98c49b4))

# [5.3.0](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.19...@react-navigation/stack@5.3.0) (2020-05-08)

### Bug Fixes

* add proper margins to the header title ([f07cd13](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/f07cd135619d635e8841aa0df0b6e687636e7408))
* include safe are insets in title's margins ([4d1e102](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/4d1e102f8c3ffab116d0195fbab3086f6345a077))

### Features

* add generic type aliases for screen props ([bea14aa](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/bea14aa26fd5cbfebc7973733c5cf1f44fd323aa)), closes [#7971](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7971)

## [5.2.19](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.18...@react-navigation/stack@5.2.19) (2020-05-05)

**Note:** Version bump only for package @react-navigation/stack

## [5.2.18](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.17...@react-navigation/stack@5.2.18) (2020-05-01)

**Note:** Version bump only for package @react-navigation/stack

## [5.2.17](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.16...@react-navigation/stack@5.2.17) (2020-05-01)

**Note:** Version bump only for package @react-navigation/stack

## [5.2.16](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.15...@react-navigation/stack@5.2.16) (2020-04-30)

**Note:** Version bump only for package @react-navigation/stack

## [5.2.15](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.14...@react-navigation/stack@5.2.15) (2020-04-30)

### Bug Fixes

* make sure the address bar hides when scrolling on web ([0a19e94](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0a19e94b23a4d2b5f22d1d9deb0544f586f475ee))

## [5.2.14](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.13...@react-navigation/stack@5.2.14) (2020-04-27)

### Bug Fixes

* don't add back the route being replaced ([a695cf9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/a695cf9c058521ccb4a83eb206dc0da7ce100032))

## [5.2.12](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.11...@react-navigation/stack@5.2.12) (2020-04-22)

### Bug Fixes

* animate card to existing closing state on gesture end ([78485ce](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/78485cea6939b9ffec76e0c4b410bc426ed93402)), closes [#7938](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7938)

## [5.2.11](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.10...@react-navigation/stack@5.2.11) (2020-04-17)

### Bug Fixes

* disable animation by default on web for stack ([dfdba8d](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/dfdba8d741abb4aa82235688d9f49e26305d2bca))
* hide inactive screens for stack on web ([#8010](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/8010)) ([82edb25](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/82edb2581bab960f206fd67368a45ad384955c97))
* ios presentation modal cuts the topOffset on the bottom ([#7943](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7943)) ([6e51f59](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/6e51f596fa85796c2a3567222f51ff914c1f6c94)), closes [#7856](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7856)

## [5.2.10](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.9...@react-navigation/stack@5.2.10) (2020-04-08)

### Bug Fixes

* make color of shadow element same as card color in stack ([f1a8bce](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/f1a8bceba5b736e9f59862a8ae819342209a46f2))
* mark type exports for all packages ([b71de6c](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/b71de6cc799143f1d0e8a0cfcc34f0a2381f9840))

## [5.2.9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.8...@react-navigation/stack@5.2.9) (2020-03-30)

### Bug Fixes

* dismiss keyboard on screen change for android ([8432e5a](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/8432e5ab25f041af8538ea7fb35e97cfcf1f983e))
* finish stack animation on CANCELLED event ([#7898](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7898)) ([d649fbc](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/d649fbc6691871f0348076bce185d11a183c02cf)), closes [#7897](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7897)
* when comparing changed routes, only check keys ([9a8fea8](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9a8fea8f2c1bdabfc5dd87e5c3ff4e7b97aef47d))

## [5.2.7](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.6...@react-navigation/stack@5.2.7) (2020-03-26)

### Bug Fixes

* add pointerEvents=box-none to overlay View ([#7871](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7871)) ([e097df8](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/e097df880adab984aae29f847003d2548cfbdce5))

## [5.2.6](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.5...@react-navigation/stack@5.2.6) (2020-03-23)

**Note:** Version bump only for package @react-navigation/stack

## [5.2.5](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.4...@react-navigation/stack@5.2.5) (2020-03-23)

### Bug Fixes

* fix swipe gestures requiring a lot of velocity to dismiss ([61f16d3](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/61f16d3f25cbbcc00d699dd09c5f4abde9b17d5a))

## [5.2.4](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.3...@react-navigation/stack@5.2.4) (2020-03-22)

### Bug Fixes

* fix swipe not dismissing card in RTL ([043924c](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/043924ca4843b6f02626532cbf4aafc7cad9fab1)), closes [#7841](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7841)

## [5.2.3](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.2...@react-navigation/stack@5.2.3) (2020-03-19)

### Bug Fixes

* use the correct velocity value in closing animation ([#7836](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7836)) ([adbfedc](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/adbfedcd58d4e3d358c6c9691710bb8e4e0d8afb))

## [5.2.2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.1...@react-navigation/stack@5.2.2) (2020-03-19)

### Bug Fixes

* don't use react-native-screens on web ([b1a65fc](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/b1a65fc73e8603ae2c06ef101a74df31e80bb9b2)), closes [#7485](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7485)
* fix blank page if stack was inside display: none before ([49f6fed](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/49f6fed6d3da878e02a9fe9115c05bcf84e332bf))
* fix closing stack using inverted gesture. ([#7824](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7824)) ([f24d3a3](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/f24d3a3461ee8a566c25ce7d13f31035b4be2691))
* initialize height and width to zero if undefined ([3df65e2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/3df65e28197db3bb8371059146546d57661c5ba3)), closes [#6789](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6789)
* only dismiss previously focused input on page change. closes [#6918](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6918) ([b1fe730](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/b1fe73097f3ad58d3ba4a8a9b875276d1d8d220c))

## [5.2.1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.2.0...@react-navigation/stack@5.2.1) (2020-03-17)

**Note:** Version bump only for package @react-navigation/stack

# [5.2.0](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.1.1...@react-navigation/stack@5.2.0) (2020-03-16)

### Bug Fixes

* fix android header title font weight ([#7720](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7720)) ([0dcaea3](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0dcaea32428484cdc9b4d56f7bf38f9f1bdf1dee))
* fix back gesture cancellation ([#7700](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7700)) ([469d054](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/469d0542c7341dc524a597d70216ba743602a51e)), closes [#6782](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6782)

### Features

* add an option to change use a custom card overlay ([#7809](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7809)) ([70029d6](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/70029d6c130f0f47de94b6a6c4cbee6afa12b405))

## [5.1.1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.1.0...@react-navigation/stack@5.1.1) (2020-03-03)

### Bug Fixes

* ignore back button press if screen isn't focused. closes [#7673](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/7673) ([296c836](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/296c836064447e055a88e43cfbbf5f9de93838f0))

# [5.1.0](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.9...@react-navigation/stack@5.1.0) (2020-02-26)

### Features

* add ability add listeners with listeners prop ([1624108](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/162410843c4f175ae107756de1c3af04d1d47aa7)), closes [#6756](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6756)

## [5.0.9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.8...@react-navigation/stack@5.0.9) (2020-02-24)

### Bug Fixes

* enhance border radius in modals on new iPhones ([#6945](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6945)) ([80a3370](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/80a337024abc53537ff4a63916cea38bb4f374bf))

## [5.0.8](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.7...@react-navigation/stack@5.0.8) (2020-02-21)

### Bug Fixes

* fix transparent header on Android ([a67b494](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/a67b49477eb500c81fedcd73bbd8102901a95170))

## [5.0.7](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.6...@react-navigation/stack@5.0.7) (2020-02-21)

### Bug Fixes

* debounce back button by default in stack header ([c7dd3a5](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/c7dd3a58b18d7a267d94009d459944c251ea74c1))
* make sure header is visibile to accessibility tools on iOS ([240ce01](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/240ce01822febac2c1aa324c01e43fdc88a235a0))

## [5.0.6](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.5...@react-navigation/stack@5.0.6) (2020-02-19)

### Bug Fixes

* add accessibilityLabel prop to back button ([bf76075](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/bf76075e0fbf51961e81e9337ef194e43cc6b986)), closes [#6895](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6895)

## [5.0.5](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.4...@react-navigation/stack@5.0.5) (2020-02-14)

**Note:** Version bump only for package @react-navigation/stack

## [5.0.4](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.3...@react-navigation/stack@5.0.4) (2020-02-14)

### Bug Fixes

* hard code header height for animation ([8f40a98](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/8f40a980862a182a9e86dbb1e4764a39d824cd70)), closes [#6818](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/6818)

## [5.0.3](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.2...@react-navigation/stack@5.0.3) (2020-02-12)

### Bug Fixes

* check if we can go baack before dispatching pop ([6c9447a](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/6c9447a38c74ca029fc9def8aca0a2d2cca9639c))

## [5.0.2](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.1...@react-navigation/stack@5.0.2) (2020-02-11)

### Bug Fixes

* provide route context to header and bottom tabs ([b6e7e08](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/b6e7e08b9a05be6c04ed21e938b9580876239116))

## [5.0.1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.71...@react-navigation/stack@5.0.1) (2020-02-10)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.71](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.70...@react-navigation/stack@5.0.0-alpha.71) (2020-02-05)

### Bug Fixes

* use addListener only when available ([f746ece](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/f746ece61b8d2c4088e5d1dc3acbf00b089ad3e2))

# [5.0.0-alpha.70](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.69...@react-navigation/stack@5.0.0-alpha.70) (2020-02-04)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.69](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.68...@react-navigation/stack@5.0.0-alpha.69) (2020-02-04)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.68](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.67...@react-navigation/stack@5.0.0-alpha.68) (2020-02-03)

### Bug Fixes

* use .native for masked view instead of .web ([abdf9d1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/abdf9d12b5c3fbde6414b50e3b6e082b67899772))

# [5.0.0-alpha.67](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.66...@react-navigation/stack@5.0.0-alpha.67) (2020-02-03)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.66](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.63...@react-navigation/stack@5.0.0-alpha.66) (2020-02-02)

### Bug Fixes

* add accessibilityRole=header to header title ([0ead266](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0ead2662ec10078b5e238f53f4607a8c712c20a4))
* add licenses ([0c159db](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0c159db4c9bc85e83b5cfe6819ab2562669a4d8f))
* disable screens when mode is modal on older expo versions ([94d7b28](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/94d7b28c0b2ce0d56c99b224610f305be6451626))
* dispatch pop early when screen is closed with gesture ([#336](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/336)) ([3d937d1](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/3d937d1e6571cd613e830d64f7b2e7426076d371)), closes [#267](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/267)
* fix shadow position for inverted animations ([5fe140e](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/5fe140e61b9d6038490b1975b99331613933eb39))
* increase epsilon in CardContainer.tsx ([9be904d](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9be904d9c4c74c23a7d1d60d81f4366c601f5082))
* make UNVERSIONED insufficient expo version ([a6f5867](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/a6f58677dce9c8446de7879014490c9ab76eceb9))
* screens integration on Android ([#294](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/294)) ([9bfb295](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9bfb29562020c61b4d5c9bee278bcb1c7bdb8b67))
* update screens for native stack ([5411816](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/54118161885738a6d20b062c7e6679f3bace8424))
* web with internal interpolation listener ([edf96d8](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/edf96d839fea3a9919e4133bd476df303d7a2b00))
* wrap navigators in gesture handler root ([41a5e1a](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/41a5e1a385aa5180abc3992a4c67077c37b998b9))

### Features

* add `animationTypeForReplace` option ([#297](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/297)) ([6262f72](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/6262f7298bff843571fb4b1a677d3beabe29833e))

# [5.0.0-alpha.64](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.63...@react-navigation/stack@5.0.0-alpha.64) (2020-02-02)

### Bug Fixes

* add accessibilityRole=header to header title ([0ead266](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0ead2662ec10078b5e238f53f4607a8c712c20a4))
* add licenses ([0c159db](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0c159db4c9bc85e83b5cfe6819ab2562669a4d8f))
* disable screens when mode is modal on older expo versions ([94d7b28](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/94d7b28c0b2ce0d56c99b224610f305be6451626))
* fix shadow position for inverted animations ([5fe140e](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/5fe140e61b9d6038490b1975b99331613933eb39))
* increase epsilon in CardContainer.tsx ([9be904d](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9be904d9c4c74c23a7d1d60d81f4366c601f5082))
* make UNVERSIONED insufficient expo version ([a6f5867](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/a6f58677dce9c8446de7879014490c9ab76eceb9))
* screens integration on Android ([#294](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/294)) ([9bfb295](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/9bfb29562020c61b4d5c9bee278bcb1c7bdb8b67))
* update screens for native stack ([5411816](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/54118161885738a6d20b062c7e6679f3bace8424))
* web with internal interpolation listener ([edf96d8](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/edf96d839fea3a9919e4133bd476df303d7a2b00))

### Features

* add `animationTypeForReplace` option ([#297](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/297)) ([6262f72](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/6262f7298bff843571fb4b1a677d3beabe29833e))

# [5.0.0-alpha.63](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.62...@react-navigation/stack@5.0.0-alpha.63) (2020-01-24)

### Bug Fixes

* pass correct previous scene to header with headerMode: screen ([16c64e7](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/16c64e729896a157b2b5bb96d6e3eead827626a0))

# [5.0.0-alpha.62](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.61...@react-navigation/stack@5.0.0-alpha.62) (2020-01-23)

### Bug Fixes

* don't use native driver on web ([0a982ee](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/0a982ee6984b24c0ba053a30223e255f3835e050))
* handle header translation for horizontal-inverted ([321fa65](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/321fa653add8366b7f24fb9de9a950064421dfc1))
* position inactivscreensws offscreen by default ([38520a9](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/38520a97ff90af0a2f89f95676487a54104068d3))
* slide the header up to hide it for vertical animation ([43d2c45](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/43d2c456beb58a8a57104ac308559cbd62998a52))
* use a fade animation for header in all presets ([fe82276](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/fe82276b1f0d1a991744e642dcfa9034fb767caf))

### Features

* emit appear and dismiss events for native stack ([f1df4a0](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/f1df4a080877b3642e748a41a5ffc2da8c449a8c))
* let the navigator specify if default can be prevented ([da67e13](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/da67e134d2157201360427d3c10da24f24cae7aa))

# [5.0.0-alpha.61](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.60...@react-navigation/stack@5.0.0-alpha.61) (2020-01-14)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.60](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.59...@react-navigation/stack@5.0.0-alpha.60) (2020-01-13)

### Bug Fixes

* make sure paths aren't aliased when building definitions ([65a5dac](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/65a5dac2bf887f4ba081ab15bd4c9870bb15697f)), closes [#265](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/265)

# [5.0.0-alpha.59](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.58...@react-navigation/stack@5.0.0-alpha.59) (2020-01-13)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.58](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.56...@react-navigation/stack@5.0.0-alpha.58) (2020-01-09)

### Bug Fixes

* change default screen change animation on web ([37d26ca](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/37d26ca994f13dd78db234309b78122a52d4550c))
* change POP behaviour to remove elements from index only ([7a3d652](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/7a3d652e847e173964a06cc9d859129ca0317861)), closes [#256](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/256)
* clamp interpolated styles ([67798af](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/67798af869dcbbf323629fc7e7cc9062d1e12c29))
* don't add header animation if mode is not float ([5470aea](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/5470aeaca2f82c0cc320f773ed0cd1672a1e338a))
* only render last 3 headers in stack ([32ffaac](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/32ffaac647fa711edf188a7929b762f5beb1df15))

# [5.0.0-alpha.57](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/compare/@react-navigation/stack@5.0.0-alpha.56...@react-navigation/stack@5.0.0-alpha.57) (2020-01-09)

### Bug Fixes

* change POP behaviour to remove elements from index only ([7a3d652](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/7a3d652e847e173964a06cc9d859129ca0317861)), closes [#256](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/issues/256)
* clamp interpolated styles ([67798af](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/67798af869dcbbf323629fc7e7cc9062d1e12c29))
* only render last 3 headers in stack ([32ffaac](https://github.com/react-navigation/react-navigation/tree/main/packages/stack/commit/32ffaac647fa711edf188a7929b762f5beb1df15))

# [5.0.0-alpha.56](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.55...@react-navigation/stack@5.0.0-alpha.56) (2020-01-07)

### Bug Fixes

* remove clamping in extrapolation of progress of stack animation ([d3f5c55](https://github.com/react-navigation/navigation-ex/commit/d3f5c55dbfbbff604c3289a40f3eccd91a60ee2e))

# [5.0.0-alpha.55](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.54...@react-navigation/stack@5.0.0-alpha.55) (2020-01-06)

### Bug Fixes

* memoize interpolated style to avoid extra work ([d8b88bd](https://github.com/react-navigation/navigation-ex/commit/d8b88bd83f57f2626d5b66bb157fd8e21a937c28))

# [5.0.0-alpha.54](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.53...@react-navigation/stack@5.0.0-alpha.54) (2020-01-05)

### Bug Fixes

* expose the header height even if not floating ([12d9083](https://github.com/react-navigation/navigation-ex/commit/12d90833eb36e9e7f229384ec8a05823b0a564d1))
* use memo for card container ([65ce20e](https://github.com/react-navigation/navigation-ex/commit/65ce20ecbc1e5f2dba9f1004cb29de03a6e5504a))

# [5.0.0-alpha.53](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.52...@react-navigation/stack@5.0.0-alpha.53) (2020-01-05)

### Bug Fixes

* compare with correct height when floating header height updates ([a9e584c](https://github.com/react-navigation/navigation-ex/commit/a9e584c3b765ae1e166a3a82b3fa0a40e8e2172a))

### Features

* expose header height in context ([133b59c](https://github.com/react-navigation/navigation-ex/commit/133b59cd175ddc899dff3b56bf3a0514c0c91ae6))

# [5.0.0-alpha.52](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.51...@react-navigation/stack@5.0.0-alpha.52) (2020-01-05)

### Features

* add headerStatusBarHeight option to stack ([b201fd2](https://github.com/react-navigation/navigation-ex/commit/b201fd20716a2f03cb9373c72281f5d396a9356d))

# [5.0.0-alpha.51](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.50...@react-navigation/stack@5.0.0-alpha.51) (2020-01-05)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.50](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.49...@react-navigation/stack@5.0.0-alpha.50) (2020-01-03)

### Bug Fixes

* keep screens for replace when animation is enabled ([7f963a7](https://github.com/react-navigation/navigation-ex/commit/7f963a74bb4d4b04134e917fe47e38e4d982afed))
* use gesture direction when using next screen's animation ([572beae](https://github.com/react-navigation/navigation-ex/commit/572beae41b326f3ef80bc2b790badf123e0071bc))

# [5.0.0-alpha.49](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.48...@react-navigation/stack@5.0.0-alpha.49) (2020-01-03)

### Bug Fixes

* dismiss keyboard on page change ([2c31d17](https://github.com/react-navigation/navigation-ex/commit/2c31d1705c4e5827b19b9cc7f3e5b05207c3238a))
* interaction manager in stack ([#237](https://github.com/react-navigation/navigation-ex/issues/237)) ([6b9b999](https://github.com/react-navigation/navigation-ex/commit/6b9b999c5b60a67ed683b84484928700d4260585))
* provide initial values for safe area to prevent blank screen ([#238](https://github.com/react-navigation/navigation-ex/issues/238)) ([77b7570](https://github.com/react-navigation/navigation-ex/commit/77b757091c0451e20bca01138629669c7da544a8))

# [5.0.0-alpha.48](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.47...@react-navigation/stack@5.0.0-alpha.48) (2020-01-01)

### Bug Fixes

* improve gesture performance ([59803f5](https://github.com/react-navigation/navigation-ex/commit/59803f54d64f85c8e46c1ebc70613a70a812f53a))
* use native driver for gestures ([9356598](https://github.com/react-navigation/navigation-ex/commit/935659899f1d4084c601fbefea4a935f9b6ce087))

# [5.0.0-alpha.47](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.46...@react-navigation/stack@5.0.0-alpha.47) (2020-01-01)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.46](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.45...@react-navigation/stack@5.0.0-alpha.46) (2019-12-19)

### Bug Fixes

* fix typescript issues ([c52a8c4](https://github.com/react-navigation/navigation-ex/commit/c52a8c46a8906812651e5259a850207fc448590e))

# [5.0.0-alpha.45](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.44...@react-navigation/stack@5.0.0-alpha.45) (2019-12-16)

### Bug Fixes

* disable style interpolation for card when animation is disabled ([c110570](https://github.com/react-navigation/navigation-ex/commit/c110570d4c89a38336f19403e6f2d0870868620e))

# [5.0.0-alpha.44](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.43...@react-navigation/stack@5.0.0-alpha.44) (2019-12-14)

### Features

* add custom theme support ([#211](https://github.com/react-navigation/navigation-ex/issues/211)) ([00fc616](https://github.com/react-navigation/navigation-ex/commit/00fc616de0572bade8aa85052cdc8290360b1d7f))

# [5.0.0-alpha.43](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.42...@react-navigation/stack@5.0.0-alpha.43) (2019-12-11)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.42](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.41...@react-navigation/stack@5.0.0-alpha.42) (2019-12-10)

### Features

* expose animation related values in context ([6cddb52](https://github.com/react-navigation/navigation-ex/commit/6cddb5238c0b1e37238c85c76e2ecb1f81517c19))

# [5.0.0-alpha.41](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.40...@react-navigation/stack@5.0.0-alpha.41) (2019-12-10)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.40](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.39...@react-navigation/stack@5.0.0-alpha.40) (2019-12-07)

### Features

* export underlying views used to build navigators ([#191](https://github.com/react-navigation/navigation-ex/issues/191)) ([d618ab3](https://github.com/react-navigation/navigation-ex/commit/d618ab382ecc5eccbcd5faa89e76f9ed2d75f405))

# [5.0.0-alpha.39](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.38...@react-navigation/stack@5.0.0-alpha.39) (2019-12-03)

### Bug Fixes

* correctly update layout on onLayout events ([eaf8847](https://github.com/react-navigation/navigation-ex/commit/eaf88478cc392fb9ff0b69c7539595920db8e010))
* disable pointerEvents on header when not focused ([87d445b](https://github.com/react-navigation/navigation-ex/commit/87d445b4e4468cf6c17787f47dd875ab8a95598a))

# [5.0.0-alpha.38](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.37...@react-navigation/stack@5.0.0-alpha.38) (2019-11-29)

### Bug Fixes

* respect custom safearea insets when calculating header height ([2750cad](https://github.com/react-navigation/navigation-ex/commit/2750cad272def2e701ba2823a6e5693cee61eff0)), closes [#190](https://github.com/react-navigation/navigation-ex/issues/190)

# [5.0.0-alpha.37](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.36...@react-navigation/stack@5.0.0-alpha.37) (2019-11-17)

### Bug Fixes

* workaround SafereaProvider causing jumping ([c17ad18](https://github.com/react-navigation/navigation-ex/commit/c17ad18b20cb05c577e1235a58ccc1c856fee086)), closes [#174](https://github.com/react-navigation/navigation-ex/issues/174)

# [5.0.0-alpha.36](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.35...@react-navigation/stack@5.0.0-alpha.36) (2019-11-10)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.35](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.34...@react-navigation/stack@5.0.0-alpha.35) (2019-11-08)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.34](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.33...@react-navigation/stack@5.0.0-alpha.34) (2019-11-04)

### Features

* support transform style for header ([#158](https://github.com/react-navigation/navigation-ex/issues/158)) ([a93a81e](https://github.com/react-navigation/navigation-ex/commit/a93a81e))

# [5.0.0-alpha.33](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.32...@react-navigation/stack@5.0.0-alpha.33) (2019-11-02)

### Bug Fixes

* add horizontal margin to centered title ([2ef5ad4](https://github.com/react-navigation/navigation-ex/commit/2ef5ad4))
* remove unnecessary paddingHorizontal on stack header ([74ee216](https://github.com/react-navigation/navigation-ex/commit/74ee216))

# [5.0.0-alpha.32](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.31...@react-navigation/stack@5.0.0-alpha.32) (2019-11-02)

### Bug Fixes

* minor tweaks for web and fix example ([67fd69a](https://github.com/react-navigation/navigation-ex/commit/67fd69a))

# [5.0.0-alpha.31](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.30...@react-navigation/stack@5.0.0-alpha.31) (2019-10-30)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.30](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.29...@react-navigation/stack@5.0.0-alpha.30) (2019-10-29)

### Bug Fixes

* keyboard manager in stack for fast swipe ([07bfc86](https://github.com/react-navigation/navigation-ex/commit/07bfc86))
* make clearKeyboardTimeout private ([876c318](https://github.com/react-navigation/navigation-ex/commit/876c318))

# [5.0.0-alpha.29](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.28...@react-navigation/stack@5.0.0-alpha.29) (2019-10-22)

### Bug Fixes

* conditions in gesture direction ([225e760](https://github.com/react-navigation/navigation-ex/commit/225e760))

# [5.0.0-alpha.28](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.27...@react-navigation/stack@5.0.0-alpha.28) (2019-10-22)

### Bug Fixes

* don't fire onOpen when screen is unmounting ([#137](https://github.com/react-navigation/navigation-ex/issues/137)) ([f22abb7](https://github.com/react-navigation/navigation-ex/commit/f22abb7)), closes [#136](https://github.com/react-navigation/navigation-ex/issues/136)
* don't keep unfocused header backgrounds visible ([031c4d2](https://github.com/react-navigation/navigation-ex/commit/031c4d2))

# [5.0.0-alpha.27](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.26...@react-navigation/stack@5.0.0-alpha.27) (2019-10-18)

### Features

* add an option to override safe area insets ([300791a](https://github.com/react-navigation/navigation-ex/commit/300791a))

# [5.0.0-alpha.26](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.25...@react-navigation/stack@5.0.0-alpha.26) (2019-10-17)

### Bug Fixes

* don't fade incoming background when fading header ([#127](https://github.com/react-navigation/navigation-ex/issues/127)) ([c6d0c19](https://github.com/react-navigation/navigation-ex/commit/c6d0c19))
* fix incorrect type ([731cf7d](https://github.com/react-navigation/navigation-ex/commit/731cf7d))
* use header height from style if specified ([442b95d](https://github.com/react-navigation/navigation-ex/commit/442b95d))

# [5.0.0-alpha.25](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.24...@react-navigation/stack@5.0.0-alpha.25) (2019-10-15)

### Bug Fixes

* don't ignore descriptors change ([9d9fe31](https://github.com/react-navigation/navigation-ex/commit/9d9fe31))
* increase hitSlop of back button on Android ([c7da1e4](https://github.com/react-navigation/navigation-ex/commit/c7da1e4))
* interpolation in iOS modal presentation ([b32cda2](https://github.com/react-navigation/navigation-ex/commit/b32cda2))
* make modal presentation mode fullscreen on landscape ([#124](https://github.com/react-navigation/navigation-ex/issues/124)) ([e789846](https://github.com/react-navigation/navigation-ex/commit/e789846))

### Features

* add a headerTitleAlign option to center or left align title ([6a0ca90](https://github.com/react-navigation/navigation-ex/commit/6a0ca90))
* export TransitionSpecs ([708dde0](https://github.com/react-navigation/navigation-ex/commit/708dde0))
* initial version of native stack ([#102](https://github.com/react-navigation/navigation-ex/issues/102)) ([ba3f718](https://github.com/react-navigation/navigation-ex/commit/ba3f718))

# [5.0.0-alpha.24](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.23...@react-navigation/stack@5.0.0-alpha.24) (2019-10-06)

### Bug Fixes

* actually expose gestureVelocityImpact in the public API ([16079d1](https://github.com/react-navigation/navigation-ex/commit/16079d1))
* don't recompute if routes didn't change ([615b523](https://github.com/react-navigation/navigation-ex/commit/615b523))
* handling vertical gesture in RTL ([#122](https://github.com/react-navigation/navigation-ex/issues/122)) ([a27ade8](https://github.com/react-navigation/navigation-ex/commit/a27ade8))
* use next screen's animation when not focused. fixes [#87](https://github.com/react-navigation/navigation-ex/issues/87) ([b4a7681](https://github.com/react-navigation/navigation-ex/commit/b4a7681))

### Features

* add gestureVelocityImpact as a prop for stack ([#123](https://github.com/react-navigation/navigation-ex/issues/123)) ([8294efc](https://github.com/react-navigation/navigation-ex/commit/8294efc))
* drop header: null in favor of more explitit headerShown option ([ba6b6ae](https://github.com/react-navigation/navigation-ex/commit/ba6b6ae))

# [5.0.0-alpha.23](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.22...@react-navigation/stack@5.0.0-alpha.23) (2019-10-03)

### Bug Fixes

* fix passing insets to interpolator ([6f5f4b7](https://github.com/react-navigation/navigation-ex/commit/6f5f4b7))
* fix vertical gesture ([a7c4a4d](https://github.com/react-navigation/navigation-ex/commit/a7c4a4d))

# [5.0.0-alpha.22](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.21...@react-navigation/stack@5.0.0-alpha.22) (2019-10-03)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.21](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.20...@react-navigation/stack@5.0.0-alpha.21) (2019-10-03)

### Bug Fixes

* add missing React import ([ece6e38](https://github.com/react-navigation/navigation-ex/commit/ece6e38))
* fix header buttons not clickable on Android. fixes [#108](https://github.com/react-navigation/navigation-ex/issues/108) ([da944cc](https://github.com/react-navigation/navigation-ex/commit/da944cc))
* keep the routes we are closing or replacing ([bc3586a](https://github.com/react-navigation/navigation-ex/commit/bc3586a))

# [5.0.0-alpha.20](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.19...@react-navigation/stack@5.0.0-alpha.20) (2019-09-27)

### Features

* export some more type aliases ([8b78d61](https://github.com/react-navigation/navigation-ex/commit/8b78d61))

# [5.0.0-alpha.19](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.18...@react-navigation/stack@5.0.0-alpha.19) (2019-09-23)

### Bug Fixes

* vertical gesture in stack ([4ee19bc](https://github.com/react-navigation/navigation-ex/commit/4ee19bc))

# [5.0.0-alpha.18](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.17...@react-navigation/stack@5.0.0-alpha.18) (2019-09-23)

### Bug Fixes

* fix header rendered behind card. closes [#108](https://github.com/react-navigation/navigation-ex/issues/108) ([2f66556](https://github.com/react-navigation/navigation-ex/commit/2f66556))

### Features

* **stack:** use Animated.Text for header title ([#105](https://github.com/react-navigation/navigation-ex/issues/105)) ([f398136](https://github.com/react-navigation/navigation-ex/commit/f398136))
* **stack:** use Animated.View for header background ([#106](https://github.com/react-navigation/navigation-ex/issues/106)) ([089390c](https://github.com/react-navigation/navigation-ex/commit/089390c))

# [5.0.0-alpha.17](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.16...@react-navigation/stack@5.0.0-alpha.17) (2019-09-17)

### Bug Fixes

* add fallbacks for non-web modules ([b4bbf9b](https://github.com/react-navigation/navigation-ex/commit/b4bbf9b)), closes [#95](https://github.com/react-navigation/navigation-ex/issues/95) [#96](https://github.com/react-navigation/navigation-ex/issues/96)
* provide navigation prop in header ([30e510d](https://github.com/react-navigation/navigation-ex/commit/30e510d))

# [5.0.0-alpha.16](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.15...@react-navigation/stack@5.0.0-alpha.16) (2019-09-16)

### Bug Fixes

* don't remove route if animation isn't finished when dragging ([#100](https://github.com/react-navigation/navigation-ex/issues/100)) ([6af8400](https://github.com/react-navigation/navigation-ex/commit/6af8400))
* tweak android q animations ([f57a91c](https://github.com/react-navigation/navigation-ex/commit/f57a91c))

### Features

* integrate `InterationManager` in stack ([9563a27](https://github.com/react-navigation/navigation-ex/commit/9563a27))
* make example run as bare react-native project as well ([#85](https://github.com/react-navigation/navigation-ex/issues/85)) ([d16c20c](https://github.com/react-navigation/navigation-ex/commit/d16c20c))

# [5.0.0-alpha.15](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.14...@react-navigation/stack@5.0.0-alpha.15) (2019-09-04)

### Features

* add approximate android Q transition ([196cce0](https://github.com/react-navigation/navigation-ex/commit/196cce0))

# [5.0.0-alpha.14](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.13...@react-navigation/stack@5.0.0-alpha.14) (2019-09-03)

### Bug Fixes

* change order of attaching nodes in card exec ([167d58c](https://github.com/react-navigation/navigation-ex/commit/167d58c))

# [5.0.0-alpha.13](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.12...@react-navigation/stack@5.0.0-alpha.13) (2019-09-01)

### Features

* useForeground if possible in stack header backButton ([aa6313c](https://github.com/react-navigation/navigation-ex/commit/aa6313c))

# [5.0.0-alpha.12](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.11...@react-navigation/stack@5.0.0-alpha.12) (2019-09-01)

### Bug Fixes

* defer running the animation to next frame ([c7a79a6](https://github.com/react-navigation/navigation-ex/commit/c7a79a6))
* stack with gesture enabled ([55ec815](https://github.com/react-navigation/navigation-ex/commit/55ec815))

# [5.0.0-alpha.11](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.10...@react-navigation/stack@5.0.0-alpha.11) (2019-09-01)

### Features

* optimizations in stack ([3f853d4](https://github.com/react-navigation/navigation-ex/commit/3f853d4))

# [5.0.0-alpha.10](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.9...@react-navigation/stack@5.0.0-alpha.10) (2019-08-31)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.9](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.8...@react-navigation/stack@5.0.0-alpha.9) (2019-08-30)

### Bug Fixes

* change interpolated style when idle to avoid messing up reanimated ([3ad2e6e](https://github.com/react-navigation/navigation-ex/commit/3ad2e6e))
* properly set animated node on gestureEnabled change ([6a8242c](https://github.com/react-navigation/navigation-ex/commit/6a8242c))

# [5.0.0-alpha.8](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.7...@react-navigation/stack@5.0.0-alpha.8) (2019-08-29)

### Bug Fixes

* allow making params optional. fixes [#80](https://github.com/react-navigation/navigation-ex/issues/80) ([a9d4813](https://github.com/react-navigation/navigation-ex/commit/a9d4813))
* fix gestures not working in stack ([8c1acc3](https://github.com/react-navigation/navigation-ex/commit/8c1acc3))

# [5.0.0-alpha.7](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.6...@react-navigation/stack@5.0.0-alpha.7) (2019-08-28)

### Bug Fixes

* fix stack nested in tab always getting reset ([dead4e8](https://github.com/react-navigation/navigation-ex/commit/dead4e8))

# [5.0.0-alpha.6](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.5...@react-navigation/stack@5.0.0-alpha.6) (2019-08-28)

### Features

* disable gesture logic when no gesture stack ([38336b0](https://github.com/react-navigation/navigation-ex/commit/38336b0))

# [5.0.0-alpha.5](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.4...@react-navigation/stack@5.0.0-alpha.5) (2019-08-27)

### Bug Fixes

* link proper descriptor for StackView ([469ec31](https://github.com/react-navigation/navigation-ex/commit/469ec31))
* set correct pointer events when active prop changes ([1bbd6ac](https://github.com/react-navigation/navigation-ex/commit/1bbd6ac))

### Features

* add hook to scroll to top on tab press ([9e1104c](https://github.com/react-navigation/navigation-ex/commit/9e1104c))
* add memoization of spring for stack ([7990cf2](https://github.com/react-navigation/navigation-ex/commit/7990cf2))

# [5.0.0-alpha.4](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.3...@react-navigation/stack@5.0.0-alpha.4) (2019-08-22)

### Bug Fixes

* fix path to typescript definitions ([f182315](https://github.com/react-navigation/navigation-ex/commit/f182315))

# [5.0.0-alpha.3](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.2...@react-navigation/stack@5.0.0-alpha.3) (2019-08-22)

**Note:** Version bump only for package @react-navigation/stack

# [5.0.0-alpha.2](https://github.com/react-navigation/navigation-ex/compare/@react-navigation/stack@5.0.0-alpha.1...@react-navigation/stack@5.0.0-alpha.2) (2019-08-21)

### Bug Fixes

* check if left button is truthy to add a left offset ([8645e36](https://github.com/react-navigation/navigation-ex/commit/8645e36))

# 5.0.0-alpha.1 (2019-08-21)

### Bug Fixes

* bunch of fixes regarding reliability of callbacks ([4878d18](https://github.com/react-navigation/navigation-ex/commit/4878d18))
* change single param to props object in onTransition callba… ([#171](https://github.com/react-navigation/navigation-ex/issues/171)) ([53f8ba9](https://github.com/react-navigation/navigation-ex/commit/53f8ba9))
* disable react-native-screens on iOS ([fb9dbf9](https://github.com/react-navigation/navigation-ex/commit/fb9dbf9))
* don't enable overlay on iOS by default ([27f0ec4](https://github.com/react-navigation/navigation-ex/commit/27f0ec4))
* don't enable screens for modal stacks ([fdf8b1a](https://github.com/react-navigation/navigation-ex/commit/fdf8b1a))
* don't ignore headerLeft if specified. fixes [#164](https://github.com/react-navigation/navigation-ex/issues/164) ([c9b2c4d](https://github.com/react-navigation/navigation-ex/commit/c9b2c4d))
* don't set a header height when a custom header is specified ([1b82e25](https://github.com/react-navigation/navigation-ex/commit/1b82e25))
* fix back button not working in header ([73424b8](https://github.com/react-navigation/navigation-ex/commit/73424b8))
* fix border radius of modal presentation ([1cf7dc5](https://github.com/react-navigation/navigation-ex/commit/1cf7dc5))
* fix broken shadows on card ([da8da3d](https://github.com/react-navigation/navigation-ex/commit/da8da3d))
* fix header tint color not applied ([879b0ea](https://github.com/react-navigation/navigation-ex/commit/879b0ea))
* fix peer deps and add git urls ([6b4fc74](https://github.com/react-navigation/navigation-ex/commit/6b4fc74))
* fix types for stack config ([bba0feb](https://github.com/react-navigation/navigation-ex/commit/bba0feb))
* fix typo preventing the screen from being cleaned up ([354da7d](https://github.com/react-navigation/navigation-ex/commit/354da7d))
* handle RTL properly ([29de72a](https://github.com/react-navigation/navigation-ex/commit/29de72a))
* hide background for unfocused header in fade ([3164527](https://github.com/react-navigation/navigation-ex/commit/3164527))
* hide overflow in wipe preset ([3f7a54d](https://github.com/react-navigation/navigation-ex/commit/3f7a54d))
* make sure components update when descriptor changes ([6792be3](https://github.com/react-navigation/navigation-ex/commit/6792be3))
* make sure left button isn't bigger than screen width / 2 ([ebc4865](https://github.com/react-navigation/navigation-ex/commit/ebc4865))
* make the header appear static when sibling of headerless screen ([55c3085](https://github.com/react-navigation/navigation-ex/commit/55c3085))
* mark descriptors as optional properties ([006a4ea](https://github.com/react-navigation/navigation-ex/commit/006a4ea))
* properly handle floating header height ([06f628b](https://github.com/react-navigation/navigation-ex/commit/06f628b))
* properly normalize velocity ([f2e3c2b](https://github.com/react-navigation/navigation-ex/commit/f2e3c2b))
* properly set pointerEvents on the views ([0589275](https://github.com/react-navigation/navigation-ex/commit/0589275))
* reduce card gesture velocity impact ([#161](https://github.com/react-navigation/navigation-ex/issues/161)) ([81b1bdf](https://github.com/react-navigation/navigation-ex/commit/81b1bdf))
* support specifying header background color in headerStyle ([98d29da](https://github.com/react-navigation/navigation-ex/commit/98d29da))
* tweak the easing for android ([78c4f25](https://github.com/react-navigation/navigation-ex/commit/78c4f25))
* tweak transition spec to prevent jumping effect ([9f3b70f](https://github.com/react-navigation/navigation-ex/commit/9f3b70f))
* use a separate shadow view for the cards ([d2397d5](https://github.com/react-navigation/navigation-ex/commit/d2397d5))
* use a shadow instead of a border for header on iOS ([6e9d05b](https://github.com/react-navigation/navigation-ex/commit/6e9d05b)), closes [#97](https://github.com/react-navigation/navigation-ex/issues/97)
* use MaskedView from @react-native-community/masked-view ([7772ac5](https://github.com/react-navigation/navigation-ex/commit/7772ac5))
* use opacity in headerStyle ([9dce71c](https://github.com/react-navigation/navigation-ex/commit/9dce71c))
* use pure component for stack items ([aeec520](https://github.com/react-navigation/navigation-ex/commit/aeec520))
* when header mode is screen, disable animations by default ([4e2afa0](https://github.com/react-navigation/navigation-ex/commit/4e2afa0))
* whitelist supported styles instead of blacklist ([1fb33c8](https://github.com/react-navigation/navigation-ex/commit/1fb33c8))

### Features

* add a canGoBack prop to header back button ([7c86cfa](https://github.com/react-navigation/navigation-ex/commit/7c86cfa))
* add cardX options in navigationOptions ([30002a1](https://github.com/react-navigation/navigation-ex/commit/30002a1))
* add comments ([c2eb482](https://github.com/react-navigation/navigation-ex/commit/c2eb482))
* add headerBackgroundStyle option ([2ea0912](https://github.com/react-navigation/navigation-ex/commit/2ea0912))
* add headerBackTitleVisible option to navigation options ([27c4861](https://github.com/react-navigation/navigation-ex/commit/27c4861))
* add headerTransparent option ([d973817](https://github.com/react-navigation/navigation-ex/commit/d973817))
* add iOS modal presentation style ([838732d](https://github.com/react-navigation/navigation-ex/commit/838732d))
* add on transition end callback ([#153](https://github.com/react-navigation/navigation-ex/issues/153)) ([51b1069](https://github.com/react-navigation/navigation-ex/commit/51b1069))
* allow specifying style interpolators in navigationOptions ([#155](https://github.com/react-navigation/navigation-ex/issues/155)) ([282cfe5](https://github.com/react-navigation/navigation-ex/commit/282cfe5))
* consider both velocity and position while calculating the next position ([#146](https://github.com/react-navigation/navigation-ex/issues/146)) ([b8237de](https://github.com/react-navigation/navigation-ex/commit/b8237de))
* implement various navigators ([f0b80ce](https://github.com/react-navigation/navigation-ex/commit/f0b80ce))
* inform whether screen is opening/closing in onTransition callbacks ([#169](https://github.com/react-navigation/navigation-ex/issues/169)) ([c0c17e9](https://github.com/react-navigation/navigation-ex/commit/c0c17e9))
* integrate react-native-screens ([#145](https://github.com/react-navigation/navigation-ex/issues/145)) ([a8460e5](https://github.com/react-navigation/navigation-ex/commit/a8460e5))
* make listeners reliable ([73b8d22](https://github.com/react-navigation/navigation-ex/commit/73b8d22))
* new implementation with reanimated ([9b176e9](https://github.com/react-navigation/navigation-ex/commit/9b176e9))
* support a function for headerTitle ([95055c1](https://github.com/react-navigation/navigation-ex/commit/95055c1))
