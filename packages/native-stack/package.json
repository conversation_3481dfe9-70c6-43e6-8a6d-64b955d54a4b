{"name": "@react-navigation/native-stack", "description": "Native stack navigator using react-native-screens", "version": "7.2.1", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "native", "stack"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/native-stack"}, "bugs": {"url": "https://github.com/software-mansion/react-native-screens/issues"}, "homepage": "https://github.com/software-mansion/react-native-screens#readme", "source": "./src/index.tsx", "main": "./lib/commonjs/index.js", "module": "./lib/module/index.js", "types": "./lib/typescript/commonjs/src/index.d.ts", "exports": {".": {"import": {"types": "./lib/typescript/module/src/index.d.ts", "default": "./lib/module/index.js"}, "require": {"types": "./lib/typescript/commonjs/src/index.d.ts", "default": "./lib/commonjs/index.js"}}}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^2.2.6", "warn-once": "^0.1.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@react-navigation/native": "workspace:^", "@testing-library/react-native": "^12.8.1", "@types/react": "~18.3.12", "del-cli": "^5.1.0", "react": "18.3.1", "react-native": "0.76.2", "react-native-builder-bob": "^0.33.2", "react-native-screens": "^4.3.0", "typescript": "^5.5.2"}, "peerDependencies": {"@react-navigation/native": "workspace:^", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json", "esm": true}]]}}