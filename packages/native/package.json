{"name": "@react-navigation/native", "description": "React Native integration for React Navigation", "version": "7.0.15", "keywords": ["react-native", "react-navigation", "ios", "android"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/native"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "source": "./src/index.tsx", "main": "./lib/commonjs/index.js", "module": "./lib/module/index.js", "types": "./lib/typescript/commonjs/src/index.d.ts", "exports": {".": {"import": {"types": "./lib/typescript/module/src/index.d.ts", "default": "./lib/module/index.js"}, "require": {"types": "./lib/typescript/commonjs/src/index.d.ts", "default": "./lib/commonjs/index.js"}}}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/core": "workspace:^", "escape-string-regexp": "^4.0.0", "fast-deep-equal": "^3.1.3", "nanoid": "3.3.8", "use-latest-callback": "^0.2.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testing-library/react-native": "^12.8.1", "@types/react": "~18.3.12", "@types/react-dom": "~18.3.1", "del-cli": "^5.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.2", "react-native-builder-bob": "^0.33.2", "typescript": "^5.5.2"}, "peerDependencies": {"react": ">= 18.2.0", "react-native": "*"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json", "esm": true}]]}}