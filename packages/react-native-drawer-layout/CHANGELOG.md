# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [4.1.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.1.1) (2024-12-12)

### Bug Fixes

* fix pressables not receiving touch in drawer ([9831292](https://github.com/react-navigation/react-navigation/commit/9831292375ddf6daca1cbf82cbe904ed4d73f7db)), closes [#12324](https://github.com/react-navigation/react-navigation/issues/12324) - by @

# [4.1.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.1.0) (2024-12-11)

### Features

* provide gesture in DrawerGestureContext ([#12326](https://github.com/react-navigation/react-navigation/issues/12326)) ([fcf3f77](https://github.com/react-navigation/react-navigation/commit/fcf3f7790a7c1c53edb5deea16d022ef8d42a5e6)) - by @gaearon

## [4.0.4](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.4) (2024-12-03)

### Bug Fixes

* remove zIndex from overlay to avoid flicker ([f662e27](https://github.com/react-navigation/react-navigation/commit/f662e274247088b142ef3087bf560f7542d4ef29)), closes [#12137](https://github.com/react-navigation/react-navigation/issues/12137) - by @

## [4.0.3](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.3) (2024-11-28)

**Note:** Version bump only for package react-native-drawer-layout

## [4.0.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.2) (2024-11-22)

**Note:** Version bump only for package react-native-drawer-layout

## [4.0.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.1) (2024-11-09)

### Bug Fixes

* **drawer:** fix gesture handler warnings to mark worklets explicitly  ([#12240](https://github.com/react-navigation/react-navigation/issues/12240)) ([13c0cc4](https://github.com/react-navigation/react-navigation/commit/13c0cc4126db6014e97ae4ff2e9d5f9af0697cfc)) - by @efstathiosntonas

# [4.0.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0) (2024-11-06)

**Note:** Version bump only for package react-native-drawer-layout

# [4.0.0-rc.11](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.11) (2024-10-24)

### Bug Fixes

* use * for react-native peer dep to support pre-release versions ([07267e5](https://github.com/react-navigation/react-navigation/commit/07267e54be752f600f808ec2898e5d76a1bc1d43)) - by @satya164

# [4.0.0-rc.10](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.10) (2024-08-01)

**Note:** Version bump only for package react-native-drawer-layout

# [4.0.0-rc.9](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.9) (2024-07-25)

### Bug Fixes

* remove transition listeners in effect cleanup ([000f7ba](https://github.com/react-navigation/react-navigation/commit/000f7ba4311364907d6efc1ed4a662c9b462e733)) - by @satya164

# [4.0.0-rc.8](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.8) (2024-07-14)

### Bug Fixes

* fix drawer animation when reduceMotion enabled. closes [#1198](https://github.com/react-navigation/react-navigation/issues/1198) ([c2caf87](https://github.com/react-navigation/react-navigation/commit/c2caf87bc5df50f665442fbb0c42794cad793a3a)) - by @satya164

# [4.0.0-rc.7](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.7) (2024-07-11)

### Bug Fixes

* upgrade react-native-builder-bob ([1575287](https://github.com/react-navigation/react-navigation/commit/1575287d40fadb97f33eb19c2914d8be3066b47a)) - by @

# [4.0.0-rc.6](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.6) (2024-07-10)

### Bug Fixes

* bump use-latest-callback to fix require ([40ddae9](https://github.com/react-navigation/react-navigation/commit/40ddae95fbbf84ff47f3447eef50ed9ddb66cab8)) - by @satya164

# [4.0.0-rc.5](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.5) (2024-07-07)

### Bug Fixes

* add missing exports entry to package.json ([ae16f52](https://github.com/react-navigation/react-navigation/commit/ae16f52adcfb58c59e5735b9caea79b1c2bfa94b)) - by @satya164
* upgrade use-latest-callback for esm compat ([187d41b](https://github.com/react-navigation/react-navigation/commit/187d41b3a139fe2a075a7809c0c4088cbd2fafdb)) - by @satya164

# [4.0.0-rc.4](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.4) (2024-07-04)

### Bug Fixes

* fix published files ([829caa0](https://github.com/react-navigation/react-navigation/commit/829caa019e125811eea5213fd380e8e1bdbe7030)) - by @

# [4.0.0-rc.3](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.3) (2024-07-04)

**Note:** Version bump only for package react-native-drawer-layout

# [4.0.0-rc.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.2) (2024-07-04)

### Features

* add package.json exports field ([1435cfe](https://github.com/react-navigation/react-navigation/commit/1435cfe3300767c221ebd4613479ad662d61efee)) - by @

# [4.0.0-rc.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.1) (2024-07-01)

### Bug Fixes

* stop using react-native field in package.json ([efc33cb](https://github.com/react-navigation/react-navigation/commit/efc33cb0c4830a84ceae034dc1278c54f1faf32d)) - by @

# [4.0.0-rc.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-rc.0) (2024-06-27)

### Bug Fixes

* fix drawer rtl on ios & android ([6fba631](https://github.com/react-navigation/react-navigation/commit/6fba631b588d83be0a731017adb46ce79ca9b2ec)) - by @satya164
* fix drawer rtl on web ([06209b9](https://github.com/react-navigation/react-navigation/commit/06209b9f04171c18b51638593d3f0fd5028a97f8)) - by @satya164

# [4.0.0-alpha.9](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.9) (2024-03-25)

### Features

* migrate drawer to new RNGH API ([#11776](https://github.com/react-navigation/react-navigation/issues/11776)) ([5d7d81e](https://github.com/react-navigation/react-navigation/commit/5d7d81e633896b3a58e86d8ab9ca0a36dcad3ab6)) - by @osdnk

# [4.0.0-alpha.8](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.8) (2024-03-14)

### Bug Fixes

* adjust drawer width according to md3 ([a88b2ea](https://github.com/react-navigation/react-navigation/commit/a88b2ea90f56d8dafbd5e1bae6a42fd9b0c73431)) - by @satya164

# [4.0.0-alpha.7](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.7) (2024-03-10)

**Note:** Version bump only for package react-native-drawer-layout

# [4.0.0-alpha.6](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.6) (2024-03-08)

### Bug Fixes

* fix right drawer layouts display over content ([#11651](https://github.com/react-navigation/react-navigation/issues/11651)) ([f6772c6](https://github.com/react-navigation/react-navigation/commit/f6772c6c2f95ed2c94c10c9632c8534fe59853b7)) - by @brannonvann
* update drawer and header styles according to material design 3 ([#11872](https://github.com/react-navigation/react-navigation/issues/11872)) ([bfa5689](https://github.com/react-navigation/react-navigation/commit/bfa568995940f956c9ec5944f2b0543eca5da546)) - by @groot007

# [4.0.0-alpha.5](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.5) (2024-02-24)

### Bug Fixes

* fix peer dependency versions ([4b93b63](https://github.com/react-navigation/react-navigation/commit/4b93b6335ce180fe879f9fbe8f2400426b5484fb)) - by @

# [4.0.0-alpha.4](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.4) (2024-02-23)

**Note:** Version bump only for package react-native-drawer-layout

# [4.0.0-alpha.3](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.3) (2023-11-17)

**Note:** Version bump only for package react-native-drawer-layout

# [4.0.0-alpha.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.2) (2023-11-12)

### Bug Fixes

* cannot resolve use-latest-callback ([#11696](https://github.com/react-navigation/react-navigation/issues/11696)) ([361bc6a](https://github.com/react-navigation/react-navigation/commit/361bc6a3840b37ae082a70e4ff6315280814c7a1)) - by @jkaveri

# [4.0.0-alpha.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.1) (2023-09-25)

### Bug Fixes

* call onGestureCancel correctly for drawer ([3cfb3e6](https://github.com/react-navigation/react-navigation/commit/3cfb3e63949f0aa6f4b14db02161dd88fd10cb12)) - by @satya164

# [4.0.0-alpha.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@4.0.0-alpha.0) (2023-09-07)

* feat!: add a direction prop to NavigationContainer to specify rtl (#11393) ([8309636](https://github.com/react-navigation/react-navigation/commit/830963653fb5a489d02f1503222629373319b39e)), closes [#11393](https://github.com/react-navigation/react-navigation/issues/11393) - by @satya164

### BREAKING CHANGES

* Previously the navigators tried to detect RTL automatically and adjust the UI. However this is problematic since we cannot detect RTL in all cases (e.g. on Web).

This adds an optional `direction` prop to `NavigationContainer` instead so that user can specify when React Navigation's UI needs to be adjusted for RTL. It defaults to the value from `I18nManager` on native platforms, however it needs to be explicitly passed for Web.

## [3.2.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@3.2.1) (2023-06-22)

**Note:** Version bump only for package react-native-drawer-layout

# [3.2.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-drawer-layout@3.2.0) (2023-03-01)

### Bug Fixes

* fix paths in sourcemap files ([368e069](https://github.com/react-navigation/react-navigation/commit/368e0691b9fb07d4b1cbe71cfe4c2f40512f93ad)) - by @satya164

### Features

* add gesture and transition events to drawer ([#11240](https://github.com/react-navigation/react-navigation/issues/11240)) ([50b94e4](https://github.com/react-navigation/react-navigation/commit/50b94e4f9518975b4fc7b46fe14d387bd9b17c7e)) - by @BeeMargarida

# 3.1.0 (2023-02-17)

### Bug Fixes

* added close drawer accessibility tap area ([#11184](https://github.com/react-navigation/react-navigation/issues/11184)) ([20ec204](https://github.com/react-navigation/react-navigation/commit/20ec2042b9d3c22388682c16fca4ef23e91ee011)) - by @mikegarfinkle

### Features

* extract drawer to a separate package ([58b7cae](https://github.com/react-navigation/react-navigation/commit/58b7caeaad00eafbcda36561e75e538e0f02c4af)) - by @satya164
