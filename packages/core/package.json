{"name": "@react-navigation/core", "description": "Core utilities for building navigators", "version": "7.4.0", "keywords": ["react", "react-native", "react-navigation"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "source": "./src/index.tsx", "main": "./lib/commonjs/index.js", "module": "./lib/module/index.js", "types": "./lib/typescript/commonjs/src/index.d.ts", "exports": {".": {"import": {"types": "./lib/typescript/module/src/index.d.ts", "default": "./lib/module/index.js"}, "require": {"types": "./lib/typescript/commonjs/src/index.d.ts", "default": "./lib/commonjs/index.js"}}}, "files": ["src", "lib", "!**/__tests__"], "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/routers": "^7.2.0", "escape-string-regexp": "^4.0.0", "nanoid": "3.3.8", "query-string": "^7.1.3", "react-is": "^18.2.0", "use-latest-callback": "^0.2.1", "use-sync-external-store": "^1.2.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testing-library/react-native": "^12.8.1", "@types/react": "~18.3.12", "@types/react-is": "^18.2.3", "@types/use-sync-external-store": "^0.0.6", "del-cli": "^5.1.0", "immer": "^10.0.3", "react": "18.3.1", "react-native-builder-bob": "^0.33.2", "react-test-renderer": "18.2.0", "typescript": "^5.5.2"}, "peerDependencies": {"react": ">= 18.2.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json", "esm": true}]]}}