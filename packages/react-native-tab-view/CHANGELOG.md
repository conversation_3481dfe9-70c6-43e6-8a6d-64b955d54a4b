# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [4.0.5](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.5) (2024-11-28)

**Note:** Version bump only for package react-native-tab-view

## [4.0.4](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.4) (2024-11-25)

**Note:** Version bump only for package react-native-tab-view

## [4.0.3](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.3) (2024-11-22)

**Note:** Version bump only for package react-native-tab-view

## [4.0.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.2) (2024-11-18)

### Bug Fixes

* fix deprecation warning for shadow styles on react-native-web ([#12253](https://github.com/react-navigation/react-navigation/issues/12253)) ([4d444f7](https://github.com/react-navigation/react-navigation/commit/4d444f77a446b622d75e6e19a3cf1c024d248a2d)), closes [#000](https://github.com/react-navigation/react-navigation/issues/000) [#000](https://github.com/react-navigation/react-navigation/issues/000) [#000](https://github.com/react-navigation/react-navigation/issues/000) - by @kubabutkiewicz

## [4.0.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.1) (2024-11-07)

### Bug Fixes

* fix custom tab bar label and style not working for material top tabs ([1a0805b](https://github.com/react-navigation/react-navigation/commit/1a0805babe288fc78ffdaf520e238ceb294ab89d)), closes [#12236](https://github.com/react-navigation/react-navigation/issues/12236) - by @satya164

# [4.0.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0) (2024-11-06)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-rc.13](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.13) (2024-10-31)

### Code Refactoring

* rename sceneContainerStyle to sceneStyle ([d1d0761](https://github.com/react-navigation/react-navigation/commit/d1d0761f0239caea1cc7b85d90de229f444f827d)) - by @satya164

### Features

* move options and commonOptions to TabView for react-native-tab-view ([3643926](https://github.com/react-navigation/react-navigation/commit/36439266d9d29cc643e7159458999a1adfb101d0)) - by @satya164

### BREAKING CHANGES

* This does the following changes:

- Remove the `sceneContainerStyle` prop from Bottom Tabs & Material Top Tabs
- Add a `sceneStyle` option to Bottom Tabs & Material Top Tabs
- Rename `sceneContainerStyle` option to `sceneStyle` for Drawer

# [4.0.0-rc.12](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.12) (2024-10-24)

### Bug Fixes

* use * for react-native peer dep to support pre-release versions ([07267e5](https://github.com/react-navigation/react-navigation/commit/07267e54be752f600f808ec2898e5d76a1bc1d43)) - by @satya164

# [4.0.0-rc.11](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.11) (2024-10-11)

### Bug Fixes

* spread key into JSX warning ([#12142](https://github.com/react-navigation/react-navigation/issues/12142)) ([f9031ec](https://github.com/react-navigation/react-navigation/commit/f9031ec3eeeda46db5c5a55dca0f24f0ea021547)) - by @jewhyena

# [4.0.0-rc.10](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.10) (2024-09-08)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-rc.9](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.9) (2024-08-08)

### Bug Fixes

* use pointer cursor only on web & iOS ([ce1ce06](https://github.com/react-navigation/react-navigation/commit/ce1ce06df75991cb25f3647bcb3e52e08dfff145)) - by @satya164

# [4.0.0-rc.8](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.8) (2024-08-01)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-rc.7](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.7) (2024-07-11)

### Bug Fixes

* upgrade react-native-builder-bob ([1575287](https://github.com/react-navigation/react-navigation/commit/1575287d40fadb97f33eb19c2914d8be3066b47a)) - by @

# [4.0.0-rc.6](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.6) (2024-07-10)

### Bug Fixes

* bump use-latest-callback to fix require ([40ddae9](https://github.com/react-navigation/react-navigation/commit/40ddae95fbbf84ff47f3447eef50ed9ddb66cab8)) - by @satya164

# [4.0.0-rc.5](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.5) (2024-07-07)

### Bug Fixes

* upgrade use-latest-callback for esm compat ([187d41b](https://github.com/react-navigation/react-navigation/commit/187d41b3a139fe2a075a7809c0c4088cbd2fafdb)) - by @satya164

# [4.0.0-rc.4](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.4) (2024-07-04)

### Bug Fixes

* fix published files ([829caa0](https://github.com/react-navigation/react-navigation/commit/829caa019e125811eea5213fd380e8e1bdbe7030)) - by @

# [4.0.0-rc.3](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.3) (2024-07-04)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-rc.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.2) (2024-07-04)

### Features

* add package.json exports field ([1435cfe](https://github.com/react-navigation/react-navigation/commit/1435cfe3300767c221ebd4613479ad662d61efee)) - by @

# [4.0.0-rc.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.1) (2024-07-01)

### Bug Fixes

* stop using react-native field in package.json ([efc33cb](https://github.com/react-navigation/react-navigation/commit/efc33cb0c4830a84ceae034dc1278c54f1faf32d)) - by @

# [4.0.0-rc.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-rc.0) (2024-06-27)

### Bug Fixes

* add hover effect to buttons on iPad & VisionOS ([2cb77c0](https://github.com/react-navigation/react-navigation/commit/2cb77c0ce42575275dd723555d0ec9ae7be32c66)) - by @satya164
* center align TabBarIndicator when width is specified ([#12030](https://github.com/react-navigation/react-navigation/issues/12030)) ([7df62fc](https://github.com/react-navigation/react-navigation/commit/7df62fcd24f13bf11d4ca08c21857c8ef57cb4aa)) - by @JAE-GYU
* update state on tab press for tab-view ([feb82e6](https://github.com/react-navigation/react-navigation/commit/feb82e62f8ed8df1fc1add00020a696c06a11e01)) - by @satya164

### Features

* pass route to badge function ([f040776](https://github.com/react-navigation/react-navigation/commit/f04077604b875da9171485c94ff977eb24da5ced)) - by @satya164

# [4.0.0-alpha.10](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.10) (2024-03-20)

### Bug Fixes

* fix some options not being passed to tab-view. fixes [#11904](https://github.com/react-navigation/react-navigation/issues/11904) ([8fcf215](https://github.com/react-navigation/react-navigation/commit/8fcf21520729f6a4485ffd247e4fa5ee78e20c81)) - by @satya164

# [4.0.0-alpha.9](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.9) (2024-03-14)

### Bug Fixes

* fix tab auto width on chrome & opera on android & linux ([#11440](https://github.com/react-navigation/react-navigation/issues/11440)) ([1ef7ed1](https://github.com/react-navigation/react-navigation/commit/1ef7ed1dcd4c8927109c62bd16ab4c9d55648d6e)), closes [#f00](https://github.com/react-navigation/react-navigation/issues/f00) [#00](https://github.com/react-navigation/react-navigation/issues/00) - by @souzabrs

# [4.0.0-alpha.8](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.8) (2024-03-10)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-alpha.7](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.7) (2024-03-08)

### Bug Fixes

* add href to tabs in material top tabs ([#11873](https://github.com/react-navigation/react-navigation/issues/11873)) ([c3459e1](https://github.com/react-navigation/react-navigation/commit/c3459e10e56446554d45130b28b232f08f491c4b)) - by @groot007

### Features

* implement tab-view new api ([#11548](https://github.com/react-navigation/react-navigation/issues/11548)) ([dca15c9](https://github.com/react-navigation/react-navigation/commit/dca15c9126f8751cfea43edc80c51d28de8f6fa6)) - by @okwasniewski

### BREAKING CHANGES

* react-native-tab-view now has a new API to address performance issues with current
implementation.

Co-authored-by: Michał Osadnik <<EMAIL>>
Co-authored-by: Satyajit Sahoo <<EMAIL>>

# [4.0.0-alpha.6](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.6) (2024-03-04)

### Bug Fixes

* update drawer and material tab bar to match latest md guidelines ([#11864](https://github.com/react-navigation/react-navigation/issues/11864)) ([8726597](https://github.com/react-navigation/react-navigation/commit/872659710dec1b097ec7c7b1dd59a6174e021b30)) - by @groot007

# [4.0.0-alpha.5](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.5) (2024-02-24)

### Bug Fixes

* fix peer dependency versions ([4b93b63](https://github.com/react-navigation/react-navigation/commit/4b93b6335ce180fe879f9fbe8f2400426b5484fb)) - by @

# [4.0.0-alpha.4](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.4) (2024-02-23)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-alpha.3](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.3) (2024-01-17)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-alpha.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.2) (2023-11-17)

**Note:** Version bump only for package react-native-tab-view

# [4.0.0-alpha.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.1) (2023-11-12)

### Bug Fixes

* cannot resolve use-latest-callback ([#11696](https://github.com/react-navigation/react-navigation/issues/11696)) ([361bc6a](https://github.com/react-navigation/react-navigation/commit/361bc6a3840b37ae082a70e4ff6315280814c7a1)) - by @jkaveri

# [4.0.0-alpha.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@4.0.0-alpha.0) (2023-09-07)

### Bug Fixes

* Allow to use `PlatformColor` in the theme ([#11570](https://github.com/react-navigation/react-navigation/issues/11570)) ([64734e7](https://github.com/react-navigation/react-navigation/commit/64734e7bc0d7f203d8e5db6abcc9a88157a5f16c)) - by @retyui
* TabBar horizontal padding for contentContainer ([#11408](https://github.com/react-navigation/react-navigation/issues/11408)) ([24c0392](https://github.com/react-navigation/react-navigation/commit/24c03924397a6e59aba9f6b74a9c5cb4b939d9e1)), closes [#8667](https://github.com/react-navigation/react-navigation/issues/8667) - by @Freddy03h

* feat!: add a direction prop to NavigationContainer to specify rtl (#11393) ([8309636](https://github.com/react-navigation/react-navigation/commit/830963653fb5a489d02f1503222629373319b39e)), closes [#11393](https://github.com/react-navigation/react-navigation/issues/11393) - by @satya164

### Features

* add children prop to tab bar indicator component ([#11566](https://github.com/react-navigation/react-navigation/issues/11566)) ([fe3b560](https://github.com/react-navigation/react-navigation/commit/fe3b56072e39a6c7b33747c4d9e3f3d6a52ec60c)) - by @grezxune
* add direction prop to TabView ([#11322](https://github.com/react-navigation/react-navigation/issues/11322)) ([46735a3](https://github.com/react-navigation/react-navigation/commit/46735a38c46ee195da836dadcf58d6a4db7a381b)) - by @okwasniewski

### BREAKING CHANGES

* Previously the navigators tried to detect RTL automatically and adjust the UI. However this is problematic since we cannot detect RTL in all cases (e.g. on Web).

This adds an optional `direction` prop to `NavigationContainer` instead so that user can specify when React Navigation's UI needs to be adjusted for RTL. It defaults to the value from `I18nManager` on native platforms, however it needs to be explicitly passed for Web.

## [3.5.2](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@3.5.2) (2023-06-22)

### Bug Fixes

* optimize tabBarItem by memoizing getter functions ([#11427](https://github.com/react-navigation/react-navigation/issues/11427)) ([1f94c8b](https://github.com/react-navigation/react-navigation/commit/1f94c8b7b2e11f09a36001ce7b512ec9468a63b5)) - by @okwasniewski

## [3.5.1](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@3.5.1) (2023-03-01)

### Bug Fixes

* fix paths in sourcemap files ([368e069](https://github.com/react-navigation/react-navigation/commit/368e0691b9fb07d4b1cbe71cfe4c2f40512f93ad)) - by @satya164

# [3.5.0](https://github.com/react-navigation/react-navigation/compare/<EMAIL>-native-tab-view@3.5.0) (2023-02-17)

### Bug Fixes

* split updating state to batches on long lists ([#11046](https://github.com/react-navigation/react-navigation/issues/11046)) ([0aa6a18](https://github.com/react-navigation/react-navigation/commit/0aa6a18f15bca0c943e22e866d178ad347a19714)) - by @okwasniewski

### Features

* add support to override pager's overScrollMode ([#11194](https://github.com/react-navigation/react-navigation/issues/11194)) ([0c4e83a](https://github.com/react-navigation/react-navigation/commit/0c4e83adf0eb8ea8d5ba6ff5520cf16dd8b82cc7)) - by @ouabing
* allow users to pass `android_ripple` config in TabView ([#11203](https://github.com/react-navigation/react-navigation/issues/11203)) ([15939d8](https://github.com/react-navigation/react-navigation/commit/15939d82cd7d77d2a75a870279d08cb18c7f9919)), closes [#11198](https://github.com/react-navigation/react-navigation/issues/11198) - by @okwasniewski
* extract drawer to a separate package ([58b7cae](https://github.com/react-navigation/react-navigation/commit/58b7caeaad00eafbcda36561e75e538e0f02c4af)) - by @satya164
