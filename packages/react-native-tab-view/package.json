{"name": "react-native-tab-view", "description": "Tab view component for React Native", "version": "4.0.5", "keywords": ["react-native-component", "react-component", "react-native", "ios", "android", "tab", "swipe", "scrollable", "coverflow"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/react-native-tab-view"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/tab-view/", "source": "./src/index.tsx", "main": "./lib/commonjs/index.js", "module": "./lib/module/index.js", "types": "./lib/typescript/commonjs/src/index.d.ts", "exports": {".": {"import": {"types": "./lib/typescript/module/src/index.d.ts", "default": "./lib/module/index.js"}, "require": {"types": "./lib/typescript/commonjs/src/index.d.ts", "default": "./lib/commonjs/index.js"}}}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"use-latest-callback": "^0.2.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testing-library/react-native": "^12.8.1", "del-cli": "^5.1.0", "react": "18.3.1", "react-native": "0.76.2", "react-native-builder-bob": "^0.33.2", "react-native-pager-view": "6.5.1", "typescript": "^5.5.2"}, "peerDependencies": {"react": ">= 18.2.0", "react-native": "*", "react-native-pager-view": ">= 6.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json", "esm": true}]]}}