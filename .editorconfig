# EditorConfig is awesome: http://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[**]
end_of_line = lf
insert_final_newline = true
indent_style = space
indent_size = 4

# Standard at: https://github.com/felixge/node-style-guide
[**.js, **.json]
trim_trailing_whitespace = true
quote_type = single
curly_bracket_next_line = false
spaces_around_operators = true
space_after_control_statements = true
space_after_anonymous_functions = false
spaces_in_brackets = false

# No Standard.  Please document a standard if different from .js
[**.yml, **.html, **.css]
trim_trailing_whitespace = true

# No standard.  Please document a standard if different from .js
[**.md]

# Standard at:
[Makefile]

[package*]
indent_style = space
indent_size = 2
