{"compilerOptions": {"rootDir": ".", "paths": {"@expensify/react-native-live-markdown": ["./src/index"]}, "allowUnreachableCode": false, "allowUnusedLabels": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "lib": ["esnext", "dom"], "module": "esnext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext", "verbatimModuleSyntax": true, "typeRoots": ["node_modules/@types"]}, "include": ["src/**/*", "mock/**/*"], "exclude": ["**/node_modules/**/*", "**/lib/**/*", "example/src/**/*", "WebExample/**/*"]}