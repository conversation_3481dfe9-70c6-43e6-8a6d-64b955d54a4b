{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "es2015", "module": "commonjs", "types": ["react-native", "react", "jest", "node"], "lib": ["esnext", "dom"], "allowJs": true, "checkJs": false, "jsx": "react", "isolatedModules": true, "strict": true, "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "declaration": true, "outDir": "./dist"}, "exclude": ["**/node_modules/**/*", "**/dist/**/*"]}