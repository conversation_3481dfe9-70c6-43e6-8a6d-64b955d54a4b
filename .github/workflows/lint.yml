name: <PERSON>t JavaScript

on:
    pull_request:
        types: [opened, synchronize]

jobs:
    lint:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Setup Node
              # v4
              uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e
              with:
                  node-version-file: ".nvmrc"
                  cache: npm
                  cache-dependency-path: package-lock.json

            - run: npm ci

            - run: npm run lint
              env:
                  CI: true

            - name: Verify there's no Prettier diff
              run: |
                npm run prettier -- --loglevel silent
                if ! git diff --name-only --exit-code; then
                  # shellcheck disable=SC2016
                  echo 'Error: Prettier diff detected! Please run `npm run prettier` and commit the changes.'
                  exit 1
                fi
