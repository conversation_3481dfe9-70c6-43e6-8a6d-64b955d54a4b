name: <PERSON>t JavaScript

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-npm-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Install node_modules
        run: npm ci

      - name: Verify there's no Prettier diff
        run: |
          npm run lint -- --fix --quiet
          if ! git diff --name-only --exit-code; then
              # shellcheck disable=SC2016
              echo 'Error: Prettier diff detected! Please run `npm run lint -- --fix` and commit the changes.'
              exit 1
          fi
