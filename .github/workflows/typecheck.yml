name: TypeScript Checks

on:
    pull_request:
        types: [opened, synchronize]

jobs:
    lint:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Setup Node
              # v4
              uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e
              with:
                  node-version-file: ".nvmrc"
                  cache: npm
                  cache-dependency-path: package-lock.json

            - run: npm ci

            - name: Type check with TypeScript
              run: npm run typecheck
              env:
                CI: true
