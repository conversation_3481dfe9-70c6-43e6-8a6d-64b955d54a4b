name: CI
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Verify dependency versions
        run: yarn check-dependency-version-consistency .

  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Lint files
        run: yarn lint

      - name: Typecheck files
        run: yarn typecheck

  unit-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run unit tests
        run: yarn test --maxWorkers=2 --coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  e2e-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Get Playwright version
        id: playwright
        run: echo "VERSION=$(node --print 'require(`@playwright/test/package.json`).version')" >> $GITHUB_OUTPUT

      - name: <PERSON><PERSON> Playwright
        uses: actions/cache/restore@v4
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ steps.playwright.outputs.VERSION }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Cache Playwright
        if: steps.playwright.outputs.cache-hit != 'true'
        uses: actions/cache/save@v4
        with:
          path: ~/.cache/ms-playwright
          key: ${{ steps.playwright.outputs.cache-primary-key }}

      - name: Build example for web
        run: yarn example expo export --platform web

      - name: Run e2e tests
        run: yarn example e2e:web

  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Build packages in the monorepo
        run: yarn lerna run prepack

      - name: Verify built type definitions are correct
        run: yarn typecheck
