name: First pull request
on:
  pull_request_target:

jobs:
  welcome:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v3
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            // Get a list of all issues created by the PR opener
            // See: https://octokit.github.io/rest.js/#pagination
            const creator = context.payload.sender.login;
            const options = github.issues.listForRepo.endpoint.merge({
              ...context.issue,
              creator,
              state: 'all'
            });

            const issues = await github.paginate(options);

            for (const issue of issues) {
              if (issue.number === context.issue.number) {
                continue;
              }

              if (issue.pull_request) {
                return; // <PERSON><PERSON> is already a contributor.
              }
            }

            const body = `Hey @${creator}! Thanks for opening your first pull request in this repo. If you haven't already, make sure to read our [contribution guidelines](https://github.com/react-navigation/react-navigation/blob/main/CONTRIBUTING.md).`;

            const comments = await github.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            });

            if (comments.data.some(comment => comment.body === body)) {
              return;
            }

            await github.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body
            });

            await github.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['first pull request'],
            });
