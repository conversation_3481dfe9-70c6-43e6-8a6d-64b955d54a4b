name: Check versions
on:
  issues:
    types:
      - opened
      - edited

jobs:
  check-versions:
    runs-on: ubuntu-latest
    steps:
      - uses: react-navigation/check-versions-action@v1.1.0
        if: contains(github.event.issue.labels.*.name, 'version-4') != true
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          required-packages: |
            @react-navigation/native
          optional-packages: |
            @react-navigation/bottom-tabs
            @react-navigation/compat
            @react-navigation/core
            @react-navigation/devtools
            @react-navigation/drawer
            @react-navigation/material-top-tabs
            @react-navigation/routers
            @react-navigation/stack
            react-native-drawer-layout
            react-native-tab-view
