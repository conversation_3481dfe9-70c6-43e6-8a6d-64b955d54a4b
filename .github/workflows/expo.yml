name: Expo Publish
on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  publish:
    name: Install and publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Setup Expo
        uses: expo/expo-github-action@v7
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Publish Expo app
        working-directory: ./example
        run: CI=1 eas update --auto
