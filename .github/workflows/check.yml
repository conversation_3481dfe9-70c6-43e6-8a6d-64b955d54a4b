name: Typecheck, lint and test
on:
  pull_request:
  merge_group:
    branches:
      - main
  push:
    branches:
      - main

jobs:
  check:
    if: github.repository == 'Expensify/react-native-live-markdown'
    runs-on: ubuntu-latest
    concurrency:
      group: check-root-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Use Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install node_modules
        run: npm ci

      - name: Typecheck library
        run: npm run typecheck -- --project tsconfig.json

      - name: Typecheck example app
        run: npm run typecheck -- --project example/tsconfig.json

      - name: Typecheck WebExample app
        run: npm run typecheck -- --project WebExample/tsconfig.json

      - name: Lint
        run: npm run lint

      - name: Test
        run: npm run test
