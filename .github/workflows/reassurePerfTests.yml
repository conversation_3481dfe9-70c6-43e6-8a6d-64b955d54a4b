name: Reassure Performance Tests

on:
  pull_request:
    types: [opened, synchronize]
    branches-ignore: [staging, production]
    paths-ignore: ['**.md', '**.sh']

jobs:
  # Note: We run baseline and delta performance checks in the same runner to reduce hardware variance across machines
  perf-tests:
    if: ${{ github.actor != 'OSBotify' }}
    runs-on: ubuntu-latest
    steps:
      # v4
      - name: Checkout
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          ref: ${{ github.event.pull_request.base.ref }}

      # v4
      - name: Setup Node
        uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies
        run: npm install

      - name: Run Reassure baseline tests
        run: npx reassure --baseline

      - name: Checkout PR head SHA
        run: |
          git fetch origin ${{ github.event.pull_request.head.sha }} --no-tags --depth=1
          git switch --force --detach ${{ github.event.pull_request.head.sha }}
          
      - name: Reinstall dependencies
        run: npm install --force

      - name: Run Reassure delta tests
        run: npx reassure --branch

      - name: Validate output.json
        id: validateReassureOutput
        uses: ./.github/actions/javascript/validateReassureOutput
        with:
          ALLOWED_DURATION_DEVIATION: 10
          ALLOWED_RELATIVE_DURATION_DEVIATION: 20
