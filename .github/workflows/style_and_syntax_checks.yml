name: Style and Syntax Checks

on:
    pull_request:
        types: [opened, synchronize]

jobs:
    checks:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              # v3
              uses: actions/checkout@ee0669bd1cc54295c223e0bb666b733df41de1c5

            - name: Setup Node
              # v3
              uses: actions/setup-node@1a4442cacd436585916779262731d5b162bc6ec7
              with:
                  node-version: 18
                  cache: npm

            - name: Install dependencies
              run: npm ci

            - name: Run ESLint
              run: npx eslint src

            - name: Run Prettier
              run: npx prettier . --check

            - name: Run TypeScript checks
              run: npm run ts
