name: Test web E2E
on:
  pull_request:
    paths:
      - .github/workflows/web-e2e-test.yml
      - src/**
      - WebExample/**
  merge_group:
    branches:
      - main
  push:
    branches:
      - main
    paths:
      - .github/workflows/web-e2e-test.yml
      - src/**
      - WebExample/**

jobs:
  test:
    if: github.repository == 'Expensify/react-native-live-markdown'
    runs-on: ubuntu-latest

    concurrency:
      group: web-e2e-test-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Use Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install node_modules
        run: npm ci

      - name: Install browsers
        run: npx playwright install --with-deps

      - name: Install dependencies for browsers
        run: npx playwright install-deps

      - name: Run Playwright tests
        run: npm run test
        working-directory: WebExample
