name: Test JavaScript

on:
    pull_request:
        types: [opened, synchronize]

jobs:
    test:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - uses: actions/cache@v3
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Setup Node
              # v4
              uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e
              with:
                  node-version-file: ".nvmrc"

            - run: npm ci

            - run: npm run test
              env:
                  CI: true
