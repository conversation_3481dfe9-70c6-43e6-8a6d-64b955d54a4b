name: Test Android build
on:
  pull_request:
    paths:
      - .github/workflows/build-android.yml
      - android/**
      - cpp/**
      - example/package.json
      - example/android/**
      - react-native.config.js
  merge_group:
    branches:
      - main
  push:
    branches:
      - main
    paths:
      - .github/workflows/build-android.yml
      - android/**
      - cpp/**
      - example/package.json
      - example/android/**
      - react-native.config.js

jobs:
  build:
    if: github.repository == 'Expensify/react-native-live-markdown'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        react-native-architecture: ['Paper', 'Fabric']
      fail-fast: false
    concurrency:
      group: build-android-${{ matrix.react-native-architecture }}-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Setup Java 17
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 17

      - name: Install node_modules
        run: npm ci

      - name: Build app
        working-directory: example/android
        run: ./gradlew assembleDebug --build-cache -PreactNativeArchitectures=arm64-v8a -PnewArchEnabled=${{ matrix.react-native-architecture == 'Fabric' && 'true' || 'false' }}
