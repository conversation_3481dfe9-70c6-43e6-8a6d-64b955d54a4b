name: Set up Node
description: Set up Node

inputs:
  IS_HYBRID_BUILD:
    description: "Indicates if node is set up for hybrid app"
    required: false
    default: 'false'

outputs:
  cache-hit:
    description: Was there a cache hit on the main node_modules?
    value: ${{ steps.cache-node-modules.outputs.cache-hit }}

runs:
  using: composite
  steps:
    - name: Remove E/App version from package-lock.json
      shell: bash
      run: jq 'del(.version, .packages[""].version)' package-lock.json > normalized-package-lock.json

    - uses: actions/setup-node@v4
      with:
        node-version-file: '.nvmrc'
        cache: npm
        cache-dependency-path: |
          normalized-package-lock.json

    - id: cache-node-modules
      # v4
      uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57
      with:
        path: node_modules
        key: ${{format('{0}-node-modules-{1}', runner.os, hashFiles('package-lock.json', 'patches/**'))}}

    - name: Install root project node packages
      if:  steps.cache-node-modules.outputs.cache-hit != 'true'
      uses: nick-fields/retry@3f757583fb1b1f940bc8ef4bf4734c8dc02a5847
      with:
        timeout_minutes: 30
        max_attempts: 3
        command: npm ci
