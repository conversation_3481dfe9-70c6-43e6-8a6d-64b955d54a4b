name: Bug report
description: Report an issue with React Navigation
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

        If this is not a bug report, please use other relevant channels:

        - [Post a feature request on <PERSON>ny](https://react-navigation.canny.io/feature-requests)
        - [Ask questions on StackOverflow using the react-navigation label](https://stackoverflow.com/questions/tagged/react-navigation)
        - [Chat with others in the #help-react-native channel on Discord](https://www.reactiflux.com/)

        Before you proceed:

        - Make sure you are on latest versions of React Navigation packages and their dependencies.
        - If you are having an issue with your machine or build tools, the issue belongs on another repository as that is outside of the scope of React Navigation.

  - type: textarea
    attributes:
      label: Current behavior
      description: |
        What code are you running and what is happening? Include a screenshot or video if it's an UI related issue.
      placeholder: Current behavior
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behavior
      description: |
        What do you expect to happen instead?
      placeholder: Expected behavior
    validations:
      required: true
  - type: input
    attributes:
      label: Reproduction
      description: |
        You must provide a way to reproduce the problem. If you don't provide a repro, the issue will be closed automatically after a specific period.

        - [Snack](https://snack.expo.dev) is the easiest way to share a repro.
        - If you can't reproduce the bug on Snack, provide a link to a GitHub repository under your username that reproduces the bug.
        - For TypeScript related issues, use [TypeScript Playground](https://www.typescriptlang.org/play) to reproduce the issue.

        Keep the repro code as simple as possible, with the minimum amount of code required to repro the issue.
      placeholder: Link to repro
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Platform
      description: |
        What are the platforms where you see the issue?
      options:
        - label: Android
        - label: iOS
        - label: Web
        - label: Windows
        - label: MacOS
  - type: checkboxes
    attributes:
      label: Packages
      description: |
        Which packages are affected by the issue?
      options:
        - label: '@react-navigation/bottom-tabs'
        - label: '@react-navigation/drawer'
        - label: '@react-navigation/material-top-tabs'
        - label: '@react-navigation/stack'
        - label: '@react-navigation/native-stack'
        - label: 'react-native-drawer-layout'
        - label: 'react-native-tab-view'
  - type: textarea
    attributes:
      label: Environment
      description: |
        What are the exact versions of packages that you are using?

        When filling the table below, **please remove the packages** that you're not using.
      value: |
        - [] I've removed the packages that I don't use

        | package                                | version |
        | -------------------------------------- | ------- |
        | @react-navigation/native               |
        | @react-navigation/bottom-tabs          |
        | @react-navigation/drawer               |
        | @react-navigation/material-top-tabs    |
        | @react-navigation/stack                |
        | @react-navigation/native-stack         |
        | react-native-drawer-layout             |
        | react-native-tab-view                  |
        | react-native-screens                   |
        | react-native-safe-area-context         |
        | react-native-gesture-handler           |
        | react-native-reanimated                |
        | react-native-pager-view                |
        | react-native                           |
        | expo                                   |
        | node                                   |
        | npm or yarn                            |
    validations:
      required: true
