blank_issues_enabled: false
contact_links:
  - name: Troubleshooting
    url: https://reactnavigation.org/docs/troubleshooting
    about: Read how to troubleshoot and fix common issues and mistakes.
  - name: Documentation
    url: https://reactnavigation.org
    about: Read the official documentation.
  - name: Feature request
    url: https://react-navigation.canny.io/feature-requests
    about: Post a feature request on <PERSON>ny.
  - name: Discussion
    url: https://github.com/react-navigation/react-navigation/discussions
    about: Discuss questions, ideas etc. and share resources related to the library.
  - name: StackOverflow
    url: https://stackoverflow.com/questions/tagged/react-navigation
    about: Ask and answer questions using the react-navigation label.
  - name: Reactiflux
    url: https://www.reactiflux.com/
    about: Chat with other community members in the `help-react-native` channel.
