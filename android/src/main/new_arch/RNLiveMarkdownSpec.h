
/**
 * This code was generated by
 * [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be
 * lost once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniH.js
 */

#pragma once

#include <ReactCommon/JavaTurboModule.h>
#include <ReactCommon/TurboModule.h>
#include <jsi/jsi.h>

#include <react/renderer/components/RNLiveMarkdownSpec/MarkdownTextInputDecoratorViewComponentDescriptor.h>

namespace facebook {
namespace react {

/**
 * JNI C++ class for module 'NativeLiveMarkdownModule'
 */
class JSI_EXPORT NativeLiveMarkdownModuleSpecJSI : public JavaTurboModule {
public:
  NativeLiveMarkdownModuleSpecJSI(const JavaTurboModule::InitParams &params);
};

JSI_EXPORT
std::shared_ptr<TurboModule>
RNLiveMarkdownSpec_ModuleProvider(const std::string &moduleName,
                                  const JavaTurboModule::InitParams &params);

} // namespace react
} // namespace facebook
