{"name": "webexample", "version": "1.0.0", "main": "App.tsx", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "playwright test"}, "dependencies": {"@expo/webpack-config": "~19.0.1", "babel-plugin-module-resolver": "^5.0.0", "expo": "^51.0.0", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-web": "~0.19.12"}, "devDependencies": {"@babel/core": "^7.24.0", "@playwright/test": "^1.43.1", "@types/node": "^20.12.7", "@types/react": "~18.2.79", "typescript": "~5.3.3"}, "private": true}