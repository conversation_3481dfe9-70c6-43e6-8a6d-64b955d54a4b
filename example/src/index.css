@font-face {
    font-family: ExpensifyMono-Neue;
    font-weight: 700;
    font-style: normal;
    src:
        url('../assets/fonts/ExpensifyNeue-Bold.woff2') format('woff2'),
        url('../assets/fonts/ExpensifyNeue-Bold.woff') format('woff');
}

* {
    color: #e7ece9;
    font-family: ExpensifyMono-Neue;
}

*:not(div) {
    margin: 0;
}

body {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

#root {
    height: 100%;
    display: flex;
}

.container {
    padding: 16px;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    background-color: #07271f;
}

.title {
    margin-bottom: 16px;
}

.react-pdf__Page {
    direction: ltr;
    margin: 1px auto -8px auto;
    background-color: transparent !important;
}

.buttons_container {
    margin-top: 16px;
    display: flex;
}

.button {
    margin: 0 8px;
    padding: 8px 16px;
    font-size: 18px;
    border: 0;
    border-radius: 16px;
    background-color: #03d47c;
    cursor: 'pointer';
}

.button_back {
    position: absolute;
    z-index: 1;
    left: 16px;
    top: 16px;
}
