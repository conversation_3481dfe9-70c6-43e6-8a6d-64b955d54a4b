/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

const cMapsDir = path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'cmaps');

module.exports = {
    entry: path.join(__dirname, 'src', 'index.js'),
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'bundle.js',
    },
    resolve: {
        modules: [path.join(__dirname, 'src'), 'node_modules'],
        alias: {
            react: path.join(__dirname, 'node_modules', 'react'),
        },
        extensions: ['.tsx', '.ts', '.js'],
    },
    devServer: {
        port: 8080,
    },
    module: {
        rules: [
            {
                test: /\.(js|jsx)?$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env', '@babel/preset-react'],
                    },
                },
            },
            {
                test: /\.(ts|tsx)$/,
                exclude: /node_modules/,
                use: 'ts-loader',
            },
            {
                test: /\.css$/,
                use: ['style-loader', 'css-loader'],
            },
            // We are importing this worker as a string by using asset/source otherwise it will default to loading via an HTTPS request later.
            // This causes issues if we have gone offline before the pdfjs web worker is set up as we won't be able to load it from the server.
            {
                // eslint-disable-next-line prefer-regex-literals
                test: new RegExp('node_modules/pdfjs-dist/build/pdf.worker.min.mjs'),
                type: 'asset/source',
            },
            {
                // eslint-disable-next-line prefer-regex-literals
                test: new RegExp('node_modules/pdfjs-dist/legacy/build/pdf.worker.min.mjs'),
                type: 'asset/source',
            },
        ],
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: path.join(__dirname, 'public', 'index.html'),
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: cMapsDir,
                    to: 'cmaps/',
                },
            ],
        }),
    ],
};
