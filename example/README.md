# Example for React Navigation

If you want to run the example from the repo,

- <PERSON>lone the repository and run `yarn` in the project root
- Run `yarn example start` to start the packager
- Build and install the development build with `yarn example android` or `yarn example ios`
- Follow the instructions in the terminal to open the app

You can also run the currently published app on Web: <https://react-navigation-example.netlify.app>
