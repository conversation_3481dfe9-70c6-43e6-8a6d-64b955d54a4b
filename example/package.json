{"name": "@react-navigation/example", "description": "Demo app to showcase various functionality of React Navigation", "version": "5.1.0", "private": true, "main": "./App.tsx", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "server": "nodemon -e '.js,.ts,.tsx' --exec \"babel-node -i '/node_modules[/\\](?react-native)/' -x '.web.tsx,.web.ts,.web.js,.tsx,.ts,.js' --config-file ./server/babel.config.js server\"", "e2e:web": "npx playwright test --config=e2e/playwright.config.ts", "e2e:native": "maestro test -e APP_ID=host.exp.exponent e2e/maestro"}, "dependencies": {"@expo/metro-runtime": "~4.0.0", "@expo/react-native-action-sheet": "^4.0.1", "@expo/vector-icons": "^14.0.3", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-masked-view/masked-view": "0.3.2", "color": "^4.2.3", "expo": "^52.0.10", "expo-asset": "~11.0.1", "expo-blur": "~14.0.3", "expo-linking": "~7.0.3", "expo-localization": "~16.0.0", "expo-splash-screen": "~0.29.12", "expo-status-bar": "~2.0.0", "expo-updates": "~0.26.8", "koa": "^2.15.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.2", "react-native-edge-to-edge": "^1.1.3", "react-native-gesture-handler": "~2.20.2", "react-native-pager-view": "6.5.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "^4.3.0", "react-native-web": "~0.19.13"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/node": "^7.22.19", "@babel/plugin-transform-strict-mode": "^7.24.7", "@playwright/test": "^1.50.1", "@types/cheerio": "^0.22.35", "@types/koa": "^2.14.0", "@types/mock-require": "^2.0.3", "@types/react": "~18.3.12", "@types/react-dom": "~18.3.1", "babel-loader": "^9.1.3", "babel-plugin-module-resolver": "^5.0.0", "babel-preset-expo": "~12.0.0", "cheerio": "^1.0.0-rc.12", "expect-type": "^0.17.3", "expo-dev-client": "~5.0.3", "mock-require-assets": "^0.0.3", "nodemon": "^3.0.3", "npm-run-all2": "^6.2.2", "react-native-builder-bob": "^0.33.2", "react-test-renderer": "18.2.0", "serve": "^14.2.1", "typescript": "^5.5.2", "yaml": "^2.5.0"}}