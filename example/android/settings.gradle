pluginManagement { includeBuild("../../node_modules/@react-native/gradle-plugin") }
plugins { id("com.facebook.react.settings") }
extensions.configure(com.facebook.react.ReactSettingsExtension){ ex -> ex.autolinkLibrariesFromCommand() }
rootProject.name = 'LiveMarkdownExample'
include ':app'
includeBuild('../../node_modules/@react-native/gradle-plugin')
// includeBuild('../node_modules/react-native') {
//     dependencySubstitution {
//         substitute(module("com.facebook.react:react-android")).using(project(":packages:react-native:ReactAndroid"))
//         substitute(module("com.facebook.react:react-native")).using(project(":packages:react-native:ReactAndroid"))
//         substitute(module("com.facebook.react:hermes-android")).using(project(":packages:react-native:ReactAndroid:hermes-engine"))
//         substitute(module("com.facebook.react:hermes-engine")).using(project(":packages:react-native:ReactAndroid:hermes-engine"))
//     }
// }
