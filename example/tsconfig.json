{
    "$schema": "https://json.schemastore.org/tsconfig",
    "compilerOptions": {
        "target": "es2016" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
        "jsx": "react" /* Specify what JSX code is generated. */,
        "module": "commonjs" /* Specify what module code is generated. */,
        "rootDir": "./src" /* Specify the root folder within your source files. */,
        "declaration": true /* Generate .d.ts files from TypeScript and JavaScript files in your project. */,
        "declarationMap": true /* Create sourcemaps for d.ts files. */,
        "outDir": "./dist" /* Specify an output folder for all emitted files. */,
        "isolatedModules": true /* Ensure that each file can be safely transpiled without relying on other imports. */,
        "allowSyntheticDefaultImports": true /* Allow 'import x from y' when a module doesn't have a default export. */,
        "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
        "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
        "strict": true /* Enable all strict type-checking options. */,
        "skipLibCheck": true /* Skip type checking all .d.ts files. */
    }
}
