{
  "extends": "../tsconfig",
  "references": [
    { "path": "../packages/bottom-tabs" },
    { "path": "../packages/core" },
    { "path": "../packages/devtools" },
    { "path": "../packages/drawer" },
    { "path": "../packages/elements" },
    { "path": "../packages/material-top-tabs" },
    { "path": "../packages/native" },
    { "path": "../packages/native-stack" },
    { "path": "../packages/routers" },
    { "path": "../packages/stack" },
    { "path": "../packages/react-native-drawer-layout" },
    { "path": "../packages/react-native-tab-view" },
  ],
  "compilerOptions": {
    "rootDir": ".",
    // Avoid expo-cli auto-generating a tsconfig
  }
}
